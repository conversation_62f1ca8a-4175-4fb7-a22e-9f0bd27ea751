import ContentStorage from '../../lib/contentStorage'

// Mock dates as fallback if database connection fails
const mockDates = [
  { date: new Date().toISOString().split('T')[0], count: 6 },
  { date: new Date(Date.now() - 86400000).toISOString().split('T')[0], count: 4 },
  { date: new Date(Date.now() - 86400000 * 2).toISOString().split('T')[0], count: 8 },
  { date: new Date(Date.now() - 86400000 * 3).toISOString().split('T')[0], count: 5 },
  { date: new Date(Date.now() - 86400000 * 4).toISOString().split('T')[0], count: 3 },
  { date: new Date(Date.now() - 86400000 * 5).toISOString().split('T')[0], count: 7 },
  { date: new Date(Date.now() - 86400000 * 6).toISOString().split('T')[0], count: 2 },
]

export default async function handler(_, res) {
  try {
    // Fetch available dates from SQL Server database using Node.js storage
    const dates = await ContentStorage.getAvailableDates(30)

    // Return data from SQL Server database
    res.status(200).json(dates)
  } catch (error) {
    console.error('Error fetching dates from SQL Server database:', error)

    // Return mock data as fallback for now
    res.status(200).json(mockDates)
  }
}
