import ContentStorage from '../../lib/contentStorage'

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const { data } = req.body

    if (!data) {
      return res.status(400).json({ error: 'No data provided' })
    }

    let summary = {
      twitter: 0,
      linkedin: 0,
      rss: 0,
      total: 0,
      timestamp: new Date().toISOString()
    }

    // Save Twitter data
    if (data.twitter && Array.isArray(data.twitter)) {
      summary.twitter = await ContentStorage.saveTwitterData(data.twitter)
    }

    // Save LinkedIn data
    if (data.linkedin && Array.isArray(data.linkedin)) {
      summary.linkedin = await ContentStorage.saveLinkedInData(data.linkedin)
    }

    // Save RSS data
    if (data.rss && Array.isArray(data.rss)) {
      summary.rss = await ContentStorage.saveRSSData(data.rss)
    }

    summary.total = summary.twitter + summary.linkedin + summary.rss

    console.log(`Saved ${summary.total} new items to database`)

    res.status(200).json(summary)
  } catch (error) {
    console.error('Error saving content to database:', error)
    
    res.status(500).json({ 
      error: 'Failed to save content to database',
      message: error.message 
    })
  }
}
