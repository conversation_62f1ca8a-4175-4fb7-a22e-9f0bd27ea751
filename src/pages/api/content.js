import ContentStorage from '../../lib/contentStorage'

export default async function handler(req, res) {
  try {
    // Get query parameters
    const { date, source, limit } = req.query

    // Parse limit parameter
    const parsedLimit = limit ? parseInt(limit, 10) : 30

    // Fetch content from database using Node.js storage
    const content = await ContentStorage.getRecentContent(
      parsedLimit,
      source || null,
      date || null
    )

    // Return data from database
    res.status(200).json(content)
  } catch (error) {
    console.error('Error fetching content from database:', error)

    // Return error response
    res.status(500).json({
      error: 'Failed to fetch content from database',
      message: error.message
    })
  }
}
