/**
 * Database connection module for the AI Dashboard.
 * Node.js version using mssql package.
 */
const sql = require('mssql');

// Database configuration
const config = {
  server: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '1433'),
  database: process.env.DB_NAME || 'aicontent',
  user: process.env.DB_USER || 'dhairya_mac',
  password: process.env.DB_PASSWORD || 'q1w2e3r4t5!',
  options: {
    encrypt: true, // Use encryption
    trustServerCertificate: true, // Trust self-signed certificates
    enableArithAbort: true,
    requestTimeout: 30000,
    connectionTimeout: 30000,
  },
  pool: {
    max: 10,
    min: 0,
    idleTimeoutMillis: 30000,
  },
};

let pool = null;

/**
 * Get database connection pool
 * @returns {Promise<sql.ConnectionPool>}
 */
async function getPool() {
  if (!pool) {
    try {
      pool = await sql.connect(config);
      console.log('Database connection established successfully');
    } catch (error) {
      console.error('Error connecting to database:', error);
      throw error;
    }
  }
  return pool;
}

/**
 * Execute a SQL query
 * @param {string} query - SQL query string
 * @param {Object} params - Query parameters
 * @returns {Promise<Object>} Query result
 */
async function executeQuery(query, params = {}) {
  try {
    const pool = await getPool();
    const request = pool.request();
    
    // Add parameters to request
    Object.keys(params).forEach(key => {
      request.input(key, params[key]);
    });
    
    const result = await request.query(query);
    return result;
  } catch (error) {
    console.error('Error executing query:', error);
    throw error;
  }
}

/**
 * Close database connection
 */
async function closeConnection() {
  if (pool) {
    await pool.close();
    pool = null;
    console.log('Database connection closed');
  }
}

module.exports = {
  getPool,
  executeQuery,
  closeConnection,
  sql
};
