/**
 * Content storage module for the AI Dashboard.
 * Node.js version using mssql package.
 */
const { executeQuery, sql } = require('./database');

class ContentStorage {
  /**
   * Get recent content from the database
   * @param {number} limit - Maximum number of items to retrieve
   * @param {string} source - Optional source filter
   * @param {string} date - Optional date filter in ISO format (YYYY-MM-DD)
   * @returns {Promise<Array>} List of content items
   */
  static async getRecentContent(limit = 50, source = null, date = null) {
    try {
      let query = `
        SELECT TOP (@limit)
          id,
          title,
          content,
          url,
          source,
          published_at,
          author_name,
          likes,
          shares,
          comments,
          summary
        FROM content
      `;
      
      const params = { limit };
      const conditions = [];
      
      if (source) {
        conditions.push('source = @source');
        params.source = source;
      }
      
      if (date) {
        conditions.push('CONVERT(DATE, published_at) = @date');
        params.date = date;
      }
      
      if (conditions.length > 0) {
        query += ' WHERE ' + conditions.join(' AND ');
      }
      
      query += ' ORDER BY published_at DESC';
      
      const result = await executeQuery(query, params);
      
      // Convert to the expected format
      return result.recordset.map(item => ({
        id: item.id,
        title: item.title,
        content: item.content,
        url: item.url,
        source: item.source,
        published_at: item.published_at ? item.published_at.toISOString() : null,
        author_name: item.author_name,
        likes: item.likes || 0,
        shares: item.shares || 0,
        comments: item.comments || 0,
        summary: item.summary
      }));
      
    } catch (error) {
      console.error('Error retrieving content from database:', error);
      throw error;
    }
  }

  /**
   * Get a list of dates that have content available
   * @param {number} limit - Maximum number of dates to retrieve
   * @returns {Promise<Array>} List of dates with content counts
   */
  static async getAvailableDates(limit = 30) {
    try {
      const query = `
        SELECT TOP (@limit)
          CONVERT(DATE, published_at) as date,
          COUNT(id) as count
        FROM content 
        GROUP BY CONVERT(DATE, published_at)
        ORDER BY CONVERT(DATE, published_at) DESC
      `;
      
      const result = await executeQuery(query, { limit });
      
      // Convert to the expected format
      return result.recordset.map(row => ({
        date: row.date ? row.date.toISOString().split('T')[0] : null,
        count: row.count
      }));
      
    } catch (error) {
      console.error('Error retrieving available dates:', error);
      throw error;
    }
  }

  /**
   * Save Twitter data to the database
   * @param {Array} tweets - List of tweet data
   * @returns {Promise<number>} Number of tweets saved
   */
  static async saveTwitterData(tweets) {
    let count = 0;
    
    for (const tweet of tweets) {
      try {
        // Check if tweet already exists
        const existingQuery = `
          SELECT id FROM content 
          WHERE source = 'twitter' AND source_id = @sourceId
        `;
        
        const existing = await executeQuery(existingQuery, { 
          sourceId: String(tweet.id) 
        });
        
        if (existing.recordset.length > 0) {
          console.log(`Tweet ${tweet.id} already exists in database`);
          continue;
        }
        
        // Insert new tweet
        const insertQuery = `
          INSERT INTO content (
            title, content, url, source, source_id, published_at, 
            author_name, likes, shares, comments
          ) VALUES (
            @title, @content, @url, @source, @sourceId, @publishedAt,
            @authorName, @likes, @shares, @comments
          )
        `;
        
        await executeQuery(insertQuery, {
          title: tweet.text ? tweet.text.substring(0, 255) : '',
          content: tweet.text || '',
          url: tweet.url || '',
          source: 'twitter',
          sourceId: String(tweet.id),
          publishedAt: tweet.created_at ? new Date(tweet.created_at) : new Date(),
          authorName: tweet.author?.name || tweet.user?.name || '',
          likes: tweet.public_metrics?.like_count || 0,
          shares: tweet.public_metrics?.retweet_count || 0,
          comments: tweet.public_metrics?.reply_count || 0
        });
        
        count++;
      } catch (error) {
        console.error(`Error saving tweet ${tweet.id}:`, error);
      }
    }
    
    console.log(`Saved ${count} new tweets to database`);
    return count;
  }

  /**
   * Save LinkedIn data to the database
   * @param {Array} posts - List of LinkedIn post data
   * @returns {Promise<number>} Number of posts saved
   */
  static async saveLinkedInData(posts) {
    let count = 0;
    
    for (const post of posts) {
      try {
        // Check if post already exists
        const existingQuery = `
          SELECT id FROM content 
          WHERE source = 'linkedin' AND source_id = @sourceId
        `;
        
        const existing = await executeQuery(existingQuery, { 
          sourceId: String(post.id) 
        });
        
        if (existing.recordset.length > 0) {
          console.log(`LinkedIn post ${post.id} already exists in database`);
          continue;
        }
        
        // Insert new post
        const insertQuery = `
          INSERT INTO content (
            title, content, url, source, source_id, published_at, 
            author_name, likes, shares, comments
          ) VALUES (
            @title, @content, @url, @source, @sourceId, @publishedAt,
            @authorName, @likes, @shares, @comments
          )
        `;
        
        await executeQuery(insertQuery, {
          title: post.text ? post.text.substring(0, 255) : '',
          content: post.text || '',
          url: post.url || '',
          source: 'linkedin',
          sourceId: String(post.id),
          publishedAt: post.created_at ? new Date(post.created_at) : new Date(),
          authorName: post.author?.name || '',
          likes: post.likes || 0,
          shares: post.shares || 0,
          comments: post.comments || 0
        });
        
        count++;
      } catch (error) {
        console.error(`Error saving LinkedIn post ${post.id}:`, error);
      }
    }
    
    console.log(`Saved ${count} new LinkedIn posts to database`);
    return count;
  }

  /**
   * Save RSS data to the database
   * @param {Array} entries - List of RSS entry data
   * @returns {Promise<number>} Number of entries saved
   */
  static async saveRSSData(entries) {
    let count = 0;
    
    for (const entry of entries) {
      try {
        // Check if entry already exists
        const existingQuery = `
          SELECT id FROM content 
          WHERE source = 'rss' AND source_id = @sourceId
        `;
        
        const existing = await executeQuery(existingQuery, { 
          sourceId: String(entry.id) 
        });
        
        if (existing.recordset.length > 0) {
          console.log(`RSS entry ${entry.id} already exists in database`);
          continue;
        }
        
        // Insert new entry
        const insertQuery = `
          INSERT INTO content (
            title, content, url, source, source_id, published_at, 
            author_name, summary
          ) VALUES (
            @title, @content, @url, @source, @sourceId, @publishedAt,
            @authorName, @summary
          )
        `;
        
        await executeQuery(insertQuery, {
          title: entry.title || '',
          content: entry.description || entry.content || '',
          url: entry.link || entry.url || '',
          source: 'rss',
          sourceId: String(entry.id),
          publishedAt: entry.published ? new Date(entry.published) : new Date(),
          authorName: entry.author || '',
          summary: entry.summary || ''
        });
        
        count++;
      } catch (error) {
        console.error(`Error saving RSS entry ${entry.id}:`, error);
      }
    }
    
    console.log(`Saved ${count} new RSS entries to database`);
    return count;
  }
}

module.exports = ContentStorage;
