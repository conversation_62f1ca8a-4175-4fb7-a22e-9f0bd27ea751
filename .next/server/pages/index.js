/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/index";
exports.ids = ["pages/index"];
exports.modules = {

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Findex.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Findex.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./src/pages/_app.js\");\n/* harmony import */ var _src_pages_index_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./src/pages/index.js */ \"./src/pages/index.js\");\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/index\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _src_pages_index_js__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Findex.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./src/components/CategoryTabs.js":
/*!****************************************!*\
  !*** ./src/components/CategoryTabs.js ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CategoryTabs)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction CategoryTabs({ categories, activeCategory, setActiveCategory }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"border-b border-gray-200 mb-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n            className: \"flex flex-wrap gap-2 sm:gap-0 sm:space-x-8\",\n            children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: `py-3 px-4 sm:px-1 border-b-2 font-medium text-sm rounded-t-lg sm:rounded-none transition-all duration-200 ${activeCategory === category.id ? \"border-blue-500 text-blue-600 bg-blue-50 sm:bg-transparent\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 hover:bg-gray-50 sm:hover:bg-transparent\"}`,\n                    onClick: ()=>setActiveCategory(category.id),\n                    children: category.name\n                }, category.id, false, {\n                    fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/components/CategoryTabs.js\",\n                    lineNumber: 6,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/components/CategoryTabs.js\",\n            lineNumber: 4,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/components/CategoryTabs.js\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/CategoryTabs.js\n");

/***/ }),

/***/ "./src/components/ContentCard.js":
/*!***************************************!*\
  !*** ./src/components/ContentCard.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ContentCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_timeUtils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/timeUtils */ \"./src/utils/timeUtils.js\");\n\n\n\nfunction ContentCard({ item, onClick }) {\n    const [imageError, setImageError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [imageLoading, setImageLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const getSourceIcon = (source)=>{\n        switch(source){\n            // case 'twitter':        // Temporarily commented out\n            //   return '🐦'          // Temporarily commented out\n            // case 'linkedin':       // Temporarily commented out\n            //   return '💼'          // Temporarily commented out\n            case \"rss\":\n                return \"\\uD83D\\uDCF0\";\n            default:\n                return \"\\uD83D\\uDCC4\";\n        }\n    };\n    const getSourceColor = (source)=>{\n        switch(source){\n            case \"rss\":\n                return \"bg-orange-100 text-orange-800\";\n            case \"twitter\":\n                return \"bg-blue-100 text-blue-800\";\n            case \"linkedin\":\n                return \"bg-blue-100 text-blue-800\";\n            default:\n                return \"bg-gray-100 text-gray-800\";\n        }\n    };\n    const handleCardClick = (e)=>{\n        // Don't trigger card click if clicking on the read more button\n        if (e.target.closest(\"a\")) return;\n        if (onClick) {\n            onClick(item);\n        } else {\n            // Default behavior: open article in new tab\n            window.open(item.url, \"_blank\", \"noopener,noreferrer\");\n        }\n    };\n    const handleImageLoad = ()=>{\n        setImageLoading(false);\n    };\n    const handleImageError = ()=>{\n        setImageError(true);\n        setImageLoading(false);\n    };\n    // Extract image from content or use a placeholder\n    const getImageUrl = ()=>{\n        // Try to extract image from content\n        if (item.content) {\n            const imgMatch = item.content.match(/<img[^>]+src=\"([^\">]+)\"/i);\n            if (imgMatch && imgMatch[1]) {\n                return imgMatch[1];\n            }\n        }\n        // Check if there's a direct image field\n        if (item.image_url) {\n            return item.image_url;\n        }\n        return null;\n    };\n    const imageUrl = getImageUrl();\n    const hasValidImage = imageUrl && !imageError;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n        className: \"group bg-white border border-gray-200 rounded-xl overflow-hidden shadow-sm card-hover cursor-pointer\",\n        onClick: handleCardClick,\n        role: \"button\",\n        tabIndex: 0,\n        onKeyDown: (e)=>{\n            if (e.key === \"Enter\" || e.key === \" \") {\n                e.preventDefault();\n                handleCardClick(e);\n            }\n        },\n        children: [\n            hasValidImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative h-48 bg-gray-100 overflow-hidden\",\n                children: [\n                    imageLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-pulse bg-gray-200 w-full h-full\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/components/ContentCard.js\",\n                            lineNumber: 94,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/components/ContentCard.js\",\n                        lineNumber: 93,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: imageUrl,\n                        alt: item.title || \"Article image\",\n                        className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\",\n                        onLoad: handleImageLoad,\n                        onError: handleImageError,\n                        loading: \"lazy\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/components/ContentCard.js\",\n                        lineNumber: 97,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/components/ContentCard.js\",\n                lineNumber: 91,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-5\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: `inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium ${getSourceColor(item.source)}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"mr-1\",\n                                        children: getSourceIcon(item.source)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/components/ContentCard.js\",\n                                        lineNumber: 112,\n                                        columnNumber: 13\n                                    }, this),\n                                    item.source.charAt(0).toUpperCase() + item.source.slice(1)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/components/ContentCard.js\",\n                                lineNumber: 111,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"time\", {\n                                className: \"text-xs text-gray-500 font-medium\",\n                                dateTime: item.published_at,\n                                title: (0,_utils_timeUtils__WEBPACK_IMPORTED_MODULE_2__.formatFullTimestamp)(item.published_at),\n                                children: (0,_utils_timeUtils__WEBPACK_IMPORTED_MODULE_2__.formatSmartDate)(item.published_at)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/components/ContentCard.js\",\n                                lineNumber: 115,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/components/ContentCard.js\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"font-bold text-gray-900 mb-3 line-clamp-2 text-lg leading-tight group-hover:text-blue-600 transition-colors duration-200\",\n                        children: item.title || \"Untitled\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/components/ContentCard.js\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: item.summary ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"inline-flex items-center text-xs text-blue-600 font-medium bg-blue-50 px-2 py-1 rounded-full mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"mr-1\",\n                                            children: \"✨\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/components/ContentCard.js\",\n                                            lineNumber: 134,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"AI Summary\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/components/ContentCard.js\",\n                                    lineNumber: 133,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-700 text-sm leading-relaxed mb-2\",\n                                    children: item.summary\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/components/ContentCard.js\",\n                                    lineNumber: 137,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 text-sm leading-relaxed line-clamp-4 mb-2\",\n                            children: item.content || \"No content available\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/components/ContentCard.js\",\n                            lineNumber: 142,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/components/ContentCard.js\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, this),\n                    item.author_name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-500 mb-3\",\n                        children: [\n                            \"By \",\n                            item.author_name\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/components/ContentCard.js\",\n                        lineNumber: 150,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: item.url,\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: \"btn-primary text-sm\",\n                                onClick: (e)=>e.stopPropagation(),\n                                children: [\n                                    \"Read Article\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"ml-1.5 w-4 h-4\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/components/ContentCard.js\",\n                                            lineNumber: 166,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/components/ContentCard.js\",\n                                        lineNumber: 165,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/components/ContentCard.js\",\n                                lineNumber: 157,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"p-2 text-gray-400 hover:text-gray-600 transition-colors duration-200\",\n                                onClick: (e)=>{\n                                    e.stopPropagation();\n                                    // Add share functionality here\n                                    if (navigator.share) {\n                                        navigator.share({\n                                            title: item.title,\n                                            url: item.url\n                                        });\n                                    }\n                                },\n                                title: \"Share article\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/components/ContentCard.js\",\n                                        lineNumber: 186,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/components/ContentCard.js\",\n                                    lineNumber: 185,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/components/ContentCard.js\",\n                                lineNumber: 171,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/components/ContentCard.js\",\n                        lineNumber: 156,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/components/ContentCard.js\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/components/ContentCard.js\",\n        lineNumber: 77,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/ContentCard.js\n");

/***/ }),

/***/ "./src/components/ContentList.js":
/*!***************************************!*\
  !*** ./src/components/ContentList.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ContentList)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _ContentCard__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ContentCard */ \"./src/components/ContentCard.js\");\n\n\nfunction ContentList({ items, onCardClick }) {\n    // Check if items exists and is an array before accessing length\n    if (!items || !Array.isArray(items) || items.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-16\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-md mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 mx-auto mb-4 text-gray-300\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            className: \"w-full h-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 1,\n                                d: \"M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/components/ContentList.js\",\n                                lineNumber: 11,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/components/ContentList.js\",\n                            lineNumber: 10,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/components/ContentList.js\",\n                        lineNumber: 9,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                        children: \"No content available\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/components/ContentList.js\",\n                        lineNumber: 14,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500 text-sm\",\n                        children: \"Try adjusting your filters or check back later for new content.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/components/ContentList.js\",\n                        lineNumber: 15,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/components/ContentList.js\",\n                lineNumber: 8,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/components/ContentList.js\",\n            lineNumber: 7,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-gray-600\",\n                    children: [\n                        \"Showing \",\n                        items.length,\n                        \" article\",\n                        items.length !== 1 ? \"s\" : \"\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/components/ContentList.js\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/components/ContentList.js\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 gap-6\",\n                children: items.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ContentCard__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        item: item,\n                        onClick: onCardClick\n                    }, item.id, false, {\n                        fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/components/ContentList.js\",\n                        lineNumber: 35,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/components/ContentList.js\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/components/ContentList.js\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/ContentList.js\n");

/***/ }),

/***/ "./src/components/DatePicker.js":
/*!**************************************!*\
  !*** ./src/components/DatePicker.js ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DatePicker)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction DatePicker({ selectedDate, onDateChange }) {\n    const [availableDates, setAvailableDates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        async function fetchDates() {\n            try {\n                setLoading(true);\n                const res = await fetch(\"/api/dates\");\n                const data = await res.json();\n                setAvailableDates(data);\n            } catch (error) {\n                console.error(\"Error fetching dates:\", error);\n            } finally{\n                setLoading(false);\n            }\n        }\n        fetchDates();\n    }, []);\n    const handleDateChange = (date)=>{\n        onDateChange(date);\n    };\n    const formatDate = (dateString)=>{\n        const date = new Date(dateString);\n        return date.toLocaleDateString(\"en-US\", {\n            weekday: \"short\",\n            month: \"short\",\n            day: \"numeric\"\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mb-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-lg font-semibold mb-3 text-gray-900\",\n                children: \"Filter by Date\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/components/DatePicker.js\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/components/DatePicker.js\",\n                        lineNumber: 43,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm text-gray-500\",\n                        children: \"Loading dates...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/components/DatePicker.js\",\n                        lineNumber: 44,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/components/DatePicker.js\",\n                lineNumber: 42,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-wrap gap-2 max-h-32 overflow-y-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>handleDateChange(null),\n                        className: `px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${selectedDate === null ? \"bg-blue-500 text-white shadow-md\" : \"bg-gray-100 text-gray-700 hover:bg-gray-200 hover:shadow-sm\"}`,\n                        children: \"All Dates\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/components/DatePicker.js\",\n                        lineNumber: 48,\n                        columnNumber: 11\n                    }, this),\n                    availableDates.map((dateItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>handleDateChange(dateItem.date),\n                            className: `px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${selectedDate === dateItem.date ? \"bg-blue-500 text-white shadow-md\" : \"bg-gray-100 text-gray-700 hover:bg-gray-200 hover:shadow-sm\"}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"hidden sm:inline\",\n                                    children: formatDate(dateItem.date)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/components/DatePicker.js\",\n                                    lineNumber: 69,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sm:hidden\",\n                                    children: new Date(dateItem.date).getDate()\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/components/DatePicker.js\",\n                                    lineNumber: 70,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-1 text-xs opacity-75\",\n                                    children: [\n                                        \"(\",\n                                        dateItem.count,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/components/DatePicker.js\",\n                                    lineNumber: 71,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, dateItem.date, true, {\n                            fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/components/DatePicker.js\",\n                            lineNumber: 60,\n                            columnNumber: 13\n                        }, this))\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/components/DatePicker.js\",\n                lineNumber: 47,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/components/DatePicker.js\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/DatePicker.js\n");

/***/ }),

/***/ "./src/pages/_app.js":
/*!***************************!*\
  !*** ./src/pages/_app.js ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"./src/styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction MyApp({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n        ...pageProps\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/pages/_app.js\",\n        lineNumber: 4,\n        columnNumber: 10\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MyApp);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvX2FwcC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBOEI7QUFFOUIsU0FBU0EsTUFBTSxFQUFFQyxTQUFTLEVBQUVDLFNBQVMsRUFBRTtJQUNyQyxxQkFBTyw4REFBQ0Q7UUFBVyxHQUFHQyxTQUFTOzs7Ozs7QUFDakM7QUFFQSxpRUFBZUYsS0FBS0EsRUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLWNvbnRlbnQtYWdncmVnYXRvci8uL3NyYy9wYWdlcy9fYXBwLmpzPzhmZGEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICcuLi9zdHlsZXMvZ2xvYmFscy5jc3MnXG5cbmZ1bmN0aW9uIE15QXBwKHsgQ29tcG9uZW50LCBwYWdlUHJvcHMgfSkge1xuICByZXR1cm4gPENvbXBvbmVudCB7Li4ucGFnZVByb3BzfSAvPlxufVxuXG5leHBvcnQgZGVmYXVsdCBNeUFwcCJdLCJuYW1lcyI6WyJNeUFwcCIsIkNvbXBvbmVudCIsInBhZ2VQcm9wcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/pages/_app.js\n");

/***/ }),

/***/ "./src/pages/index.js":
/*!****************************!*\
  !*** ./src/pages/index.js ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ContentList__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/ContentList */ \"./src/components/ContentList.js\");\n/* harmony import */ var _components_CategoryTabs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/CategoryTabs */ \"./src/components/CategoryTabs.js\");\n/* harmony import */ var _components_DatePicker__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/DatePicker */ \"./src/components/DatePicker.js\");\n\n\n\n\n\n\nfunction Home() {\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeCategory, setActiveCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [selectedDate, setSelectedDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        async function fetchContent() {\n            try {\n                setLoading(true);\n                // Build URL with query parameters\n                let url = \"/api/content\";\n                const params = new URLSearchParams();\n                if (selectedDate) {\n                    params.append(\"date\", selectedDate);\n                }\n                if (activeCategory !== \"all\") {\n                    params.append(\"source\", activeCategory);\n                }\n                // Add params to URL if any exist\n                if (params.toString()) {\n                    url += `?${params.toString()}`;\n                }\n                const res = await fetch(url);\n                const data = await res.json();\n                setContent(data);\n            } catch (error) {\n                console.error(\"Error fetching content:\", error);\n            } finally{\n                setLoading(false);\n            }\n        }\n        fetchContent();\n    }, [\n        activeCategory,\n        selectedDate\n    ]);\n    const categories = [\n        {\n            id: \"all\",\n            name: \"All\"\n        },\n        // { id: 'twitter', name: 'Twitter' },     // Temporarily commented out\n        // { id: 'linkedin', name: 'LinkedIn' },   // Temporarily commented out\n        {\n            id: \"rss\",\n            name: \"News\"\n        }\n    ];\n    // Make sure content is an array before filtering\n    const filteredContent = Array.isArray(content) ? content : [];\n    const handleCardClick = (item)=>{\n        // Optional: Add analytics or custom behavior here\n        console.log(\"Card clicked:\", item.title);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"AI Content Aggregator\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/pages/index.js\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Daily curated AI content with intelligent summaries\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/pages/index.js\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/pages/index.js\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/pages/index.js\",\n                        lineNumber: 70,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/pages/index.js\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"mb-8 sm:mb-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center sm:text-left\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-3 sm:mb-4\",\n                                    children: \"AI Content Aggregator\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/pages/index.js\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 text-lg sm:text-xl max-w-2xl\",\n                                    children: \"Your daily dose of AI news, research, and discussions with intelligent summaries\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/pages/index.js\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/pages/index.js\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/pages/index.js\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-4 sm:p-6 mb-6 sm:mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col lg:flex-row lg:justify-between lg:items-start gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CategoryTabs__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        categories: categories,\n                                        activeCategory: activeCategory,\n                                        setActiveCategory: setActiveCategory\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/pages/index.js\",\n                                        lineNumber: 90,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/pages/index.js\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:w-80\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DatePicker__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        selectedDate: selectedDate,\n                                        onDateChange: setSelectedDate\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/pages/index.js\",\n                                        lineNumber: 98,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/pages/index.js\",\n                                    lineNumber: 97,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/pages/index.js\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/pages/index.js\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center justify-center py-16 sm:py-24\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/pages/index.js\",\n                                    lineNumber: 110,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 text-lg\",\n                                    children: \"Loading content...\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/pages/index.js\",\n                                    lineNumber: 111,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/pages/index.js\",\n                            lineNumber: 109,\n                            columnNumber: 13\n                        }, this) : filteredContent.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ContentList__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            items: filteredContent,\n                            onCardClick: handleCardClick\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/pages/index.js\",\n                            lineNumber: 114,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-16 sm:py-24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-md mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-20 h-20 mx-auto mb-6 text-gray-300\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            className: \"w-full h-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 1,\n                                                d: \"M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.291-1.007-5.824-2.709M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/pages/index.js\",\n                                                lineNumber: 123,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/pages/index.js\",\n                                            lineNumber: 122,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/pages/index.js\",\n                                        lineNumber: 121,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-gray-900 mb-3\",\n                                        children: \"No content available\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/pages/index.js\",\n                                        lineNumber: 126,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500 mb-6\",\n                                        children: \"No articles match your current filters. Try adjusting your selection or check back later for new content.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/pages/index.js\",\n                                        lineNumber: 127,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setSelectedDate(null);\n                                            setActiveCategory(\"all\");\n                                        },\n                                        className: \"btn-primary\",\n                                        children: \"Reset Filters\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/pages/index.js\",\n                                        lineNumber: 130,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/pages/index.js\",\n                                lineNumber: 120,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/pages/index.js\",\n                            lineNumber: 119,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/pages/index.js\",\n                        lineNumber: 107,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/pages/index.js\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Projects/AI Dashboard/AI-Content-Dashboard/src/pages/index.js\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/index.js\n");

/***/ }),

/***/ "./src/utils/timeUtils.js":
/*!********************************!*\
  !*** ./src/utils/timeUtils.js ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatFullTimestamp: () => (/* binding */ formatFullTimestamp),\n/* harmony export */   formatRelativeTime: () => (/* binding */ formatRelativeTime),\n/* harmony export */   formatSmartDate: () => (/* binding */ formatSmartDate)\n/* harmony export */ });\n/* harmony import */ var date_fns_formatDistanceToNow__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! date-fns/formatDistanceToNow */ \"date-fns/formatDistanceToNow\");\n/* harmony import */ var date_fns_formatDistanceToNow__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(date_fns_formatDistanceToNow__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var date_fns_format__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! date-fns/format */ \"date-fns/format\");\n/* harmony import */ var date_fns_format__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(date_fns_format__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var date_fns_isToday__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! date-fns/isToday */ \"date-fns/isToday\");\n/* harmony import */ var date_fns_isToday__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(date_fns_isToday__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var date_fns_isYesterday__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! date-fns/isYesterday */ \"date-fns/isYesterday\");\n/* harmony import */ var date_fns_isYesterday__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(date_fns_isYesterday__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var date_fns_parseISO__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! date-fns/parseISO */ \"date-fns/parseISO\");\n/* harmony import */ var date_fns_parseISO__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(date_fns_parseISO__WEBPACK_IMPORTED_MODULE_4__);\n/**\n * Utility functions for time formatting and display\n */ \n\n\n\n\n/**\n * Format a date for display with smart relative/absolute formatting\n * @param {string|Date} dateInput - The date to format\n * @returns {string} Formatted date string\n */ function formatSmartDate(dateInput) {\n    try {\n        const date = typeof dateInput === \"string\" ? date_fns_parseISO__WEBPACK_IMPORTED_MODULE_4___default()(dateInput) : dateInput;\n        if (!date || isNaN(date.getTime())) {\n            return \"Unknown date\";\n        }\n        const now = new Date();\n        const diffInHours = Math.abs(now - date) / (1000 * 60 * 60);\n        // If less than 1 hour ago, show minutes\n        if (diffInHours < 1) {\n            const diffInMinutes = Math.floor(Math.abs(now - date) / (1000 * 60));\n            if (diffInMinutes < 1) {\n                return \"Just now\";\n            }\n            return `${diffInMinutes}m ago`;\n        }\n        // If less than 24 hours ago, show hours\n        if (diffInHours < 24) {\n            const hours = Math.floor(diffInHours);\n            return `${hours}h ago`;\n        }\n        // If today, show time\n        if (date_fns_isToday__WEBPACK_IMPORTED_MODULE_2___default()(date)) {\n            return date_fns_format__WEBPACK_IMPORTED_MODULE_1___default()(date, \"h:mm a\");\n        }\n        // If yesterday, show \"Yesterday\"\n        if (date_fns_isYesterday__WEBPACK_IMPORTED_MODULE_3___default()(date)) {\n            return \"Yesterday\";\n        }\n        // If within the last week, show day name\n        if (diffInHours < 24 * 7) {\n            return date_fns_format__WEBPACK_IMPORTED_MODULE_1___default()(date, \"EEEE\");\n        }\n        // If within the current year, show month and day\n        if (date.getFullYear() === now.getFullYear()) {\n            return date_fns_format__WEBPACK_IMPORTED_MODULE_1___default()(date, \"MMM d\");\n        }\n        // Otherwise, show full date\n        return date_fns_format__WEBPACK_IMPORTED_MODULE_1___default()(date, \"MMM d, yyyy\");\n    } catch (error) {\n        console.error(\"Error formatting date:\", error);\n        return \"Invalid date\";\n    }\n}\n/**\n * Get a detailed timestamp for tooltips\n * @param {string|Date} dateInput - The date to format\n * @returns {string} Full timestamp string\n */ function formatFullTimestamp(dateInput) {\n    try {\n        const date = typeof dateInput === \"string\" ? date_fns_parseISO__WEBPACK_IMPORTED_MODULE_4___default()(dateInput) : dateInput;\n        if (!date || isNaN(date.getTime())) {\n            return \"Unknown date\";\n        }\n        return date_fns_format__WEBPACK_IMPORTED_MODULE_1___default()(date, \"EEEE, MMMM d, yyyy 'at' h:mm a\");\n    } catch (error) {\n        console.error(\"Error formatting full timestamp:\", error);\n        return \"Invalid date\";\n    }\n}\n/**\n * Get relative time with better precision\n * @param {string|Date} dateInput - The date to format\n * @returns {string} Relative time string\n */ function formatRelativeTime(dateInput) {\n    try {\n        const date = typeof dateInput === \"string\" ? date_fns_parseISO__WEBPACK_IMPORTED_MODULE_4___default()(dateInput) : dateInput;\n        if (!date || isNaN(date.getTime())) {\n            return \"Unknown time\";\n        }\n        return date_fns_formatDistanceToNow__WEBPACK_IMPORTED_MODULE_0___default()(date, {\n            addSuffix: true,\n            includeSeconds: true\n        });\n    } catch (error) {\n        console.error(\"Error formatting relative time:\", error);\n        return \"Invalid time\";\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/timeUtils.js\n");

/***/ }),

/***/ "./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ (() => {



/***/ }),

/***/ "date-fns/format":
/*!**********************************!*\
  !*** external "date-fns/format" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("date-fns/format");

/***/ }),

/***/ "date-fns/formatDistanceToNow":
/*!***********************************************!*\
  !*** external "date-fns/formatDistanceToNow" ***!
  \***********************************************/
/***/ ((module) => {

"use strict";
module.exports = require("date-fns/formatDistanceToNow");

/***/ }),

/***/ "date-fns/isToday":
/*!***********************************!*\
  !*** external "date-fns/isToday" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("date-fns/isToday");

/***/ }),

/***/ "date-fns/isYesterday":
/*!***************************************!*\
  !*** external "date-fns/isYesterday" ***!
  \***************************************/
/***/ ((module) => {

"use strict";
module.exports = require("date-fns/isYesterday");

/***/ }),

/***/ "date-fns/parseISO":
/*!************************************!*\
  !*** external "date-fns/parseISO" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("date-fns/parseISO");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/head");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Findex.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();