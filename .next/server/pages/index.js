/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/index";
exports.ids = ["pages/index"];
exports.modules = {

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Findex.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Findex.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./src/pages/_app.js\");\n/* harmony import */ var _src_pages_index_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./src/pages/index.js */ \"./src/pages/index.js\");\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/index\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _src_pages_index_js__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Findex.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./src/components/CategoryTabs.js":
/*!****************************************!*\
  !*** ./src/components/CategoryTabs.js ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CategoryTabs)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction CategoryTabs({ categories, activeCategory, setActiveCategory }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"border-b border-gray-200 mb-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n            className: \"flex space-x-8\",\n            children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: `py-4 px-1 border-b-2 font-medium text-sm ${activeCategory === category.id ? \"border-blue-500 text-blue-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"}`,\n                    onClick: ()=>setActiveCategory(category.id),\n                    children: category.name\n                }, category.id, false, {\n                    fileName: \"/Users/<USER>/Documents/AI Dashboard/src/components/CategoryTabs.js\",\n                    lineNumber: 6,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Dashboard/src/components/CategoryTabs.js\",\n            lineNumber: 4,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Dashboard/src/components/CategoryTabs.js\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9DYXRlZ29yeVRhYnMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFlLFNBQVNBLGFBQWEsRUFBRUMsVUFBVSxFQUFFQyxjQUFjLEVBQUVDLGlCQUFpQixFQUFFO0lBQ3BGLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVO2tCQUNiLDRFQUFDQztZQUFJRCxXQUFVO3NCQUNaSixXQUFXTSxHQUFHLENBQUNDLENBQUFBLHlCQUNkLDhEQUFDQztvQkFFQ0osV0FBVyxDQUFDLHlDQUF5QyxFQUNuREgsbUJBQW1CTSxTQUFTRSxFQUFFLEdBQzFCLGtDQUNBLDZFQUNMLENBQUM7b0JBQ0ZDLFNBQVMsSUFBTVIsa0JBQWtCSyxTQUFTRSxFQUFFOzhCQUUzQ0YsU0FBU0ksSUFBSTttQkFSVEosU0FBU0UsRUFBRTs7Ozs7Ozs7Ozs7Ozs7O0FBYzVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktY29udGVudC1hZ2dyZWdhdG9yLy4vc3JjL2NvbXBvbmVudHMvQ2F0ZWdvcnlUYWJzLmpzPzNjZmIiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQ2F0ZWdvcnlUYWJzKHsgY2F0ZWdvcmllcywgYWN0aXZlQ2F0ZWdvcnksIHNldEFjdGl2ZUNhdGVnb3J5IH0pIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImJvcmRlci1iIGJvcmRlci1ncmF5LTIwMCBtYi02XCI+XG4gICAgICA8bmF2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC04XCI+XG4gICAgICAgIHtjYXRlZ29yaWVzLm1hcChjYXRlZ29yeSA9PiAoXG4gICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAga2V5PXtjYXRlZ29yeS5pZH1cbiAgICAgICAgICAgIGNsYXNzTmFtZT17YHB5LTQgcHgtMSBib3JkZXItYi0yIGZvbnQtbWVkaXVtIHRleHQtc20gJHtcbiAgICAgICAgICAgICAgYWN0aXZlQ2F0ZWdvcnkgPT09IGNhdGVnb3J5LmlkXG4gICAgICAgICAgICAgICAgPyAnYm9yZGVyLWJsdWUtNTAwIHRleHQtYmx1ZS02MDAnXG4gICAgICAgICAgICAgICAgOiAnYm9yZGVyLXRyYW5zcGFyZW50IHRleHQtZ3JheS01MDAgaG92ZXI6dGV4dC1ncmF5LTcwMCBob3Zlcjpib3JkZXItZ3JheS0zMDAnXG4gICAgICAgICAgICB9YH1cbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEFjdGl2ZUNhdGVnb3J5KGNhdGVnb3J5LmlkKX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICB7Y2F0ZWdvcnkubmFtZX1cbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgKSl9XG4gICAgICA8L25hdj5cbiAgICA8L2Rpdj5cbiAgKVxufSJdLCJuYW1lcyI6WyJDYXRlZ29yeVRhYnMiLCJjYXRlZ29yaWVzIiwiYWN0aXZlQ2F0ZWdvcnkiLCJzZXRBY3RpdmVDYXRlZ29yeSIsImRpdiIsImNsYXNzTmFtZSIsIm5hdiIsIm1hcCIsImNhdGVnb3J5IiwiYnV0dG9uIiwiaWQiLCJvbkNsaWNrIiwibmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/components/CategoryTabs.js\n");

/***/ }),

/***/ "./src/components/ContentCard.js":
/*!***************************************!*\
  !*** ./src/components/ContentCard.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ContentCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var date_fns_formatDistanceToNow__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! date-fns/formatDistanceToNow */ \"date-fns/formatDistanceToNow\");\n/* harmony import */ var date_fns_formatDistanceToNow__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(date_fns_formatDistanceToNow__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction ContentCard({ item }) {\n    const getSourceIcon = (source)=>{\n        switch(source){\n            case \"twitter\":\n                return \"\\uD83D\\uDC26\";\n            case \"linkedin\":\n                return \"\\uD83D\\uDCBC\";\n            case \"rss\":\n                return \"\\uD83D\\uDCF0\";\n            default:\n                return \"\\uD83D\\uDCC4\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"border rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm text-gray-500\",\n                            children: [\n                                getSourceIcon(item.source),\n                                \" \",\n                                item.source.charAt(0).toUpperCase() + item.source.slice(1)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI Dashboard/src/components/ContentCard.js\",\n                            lineNumber: 21,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-gray-400\",\n                            children: date_fns_formatDistanceToNow__WEBPACK_IMPORTED_MODULE_1___default()(new Date(item.published_at), {\n                                addSuffix: true\n                            })\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI Dashboard/src/components/ContentCard.js\",\n                            lineNumber: 24,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI Dashboard/src/components/ContentCard.js\",\n                    lineNumber: 20,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"font-semibold mb-2 line-clamp-2\",\n                    children: item.title || \"Untitled\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI Dashboard/src/components/ContentCard.js\",\n                    lineNumber: 29,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-gray-600 mb-4 line-clamp-3\",\n                    children: item.content || \"No content available\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI Dashboard/src/components/ContentCard.js\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                    href: item.url,\n                    target: \"_blank\",\n                    rel: \"noopener noreferrer\",\n                    className: \"text-blue-600 hover:text-blue-800 text-sm font-medium\",\n                    children: \"Read more →\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI Dashboard/src/components/ContentCard.js\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/AI Dashboard/src/components/ContentCard.js\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Dashboard/src/components/ContentCard.js\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/ContentCard.js\n");

/***/ }),

/***/ "./src/components/ContentList.js":
/*!***************************************!*\
  !*** ./src/components/ContentList.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ContentList)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _ContentCard__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ContentCard */ \"./src/components/ContentCard.js\");\n\n\nfunction ContentList({ items }) {\n    // Check if items exists and is an array before accessing length\n    if (!items || !Array.isArray(items) || items.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-12\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-gray-500\",\n                children: \"No content available\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Dashboard/src/components/ContentList.js\",\n                lineNumber: 8,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Dashboard/src/components/ContentList.js\",\n            lineNumber: 7,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n        children: items.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ContentCard__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                item: item\n            }, item.id, false, {\n                fileName: \"/Users/<USER>/Documents/AI Dashboard/src/components/ContentList.js\",\n                lineNumber: 16,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Dashboard/src/components/ContentList.js\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9Db250ZW50TGlzdC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUF1QztBQUV4QixTQUFTQyxZQUFZLEVBQUVDLEtBQUssRUFBRTtJQUMzQyxnRUFBZ0U7SUFDaEUsSUFBSSxDQUFDQSxTQUFTLENBQUNDLE1BQU1DLE9BQU8sQ0FBQ0YsVUFBVUEsTUFBTUcsTUFBTSxLQUFLLEdBQUc7UUFDekQscUJBQ0UsOERBQUNDO1lBQUlDLFdBQVU7c0JBQ2IsNEVBQUNDO2dCQUFFRCxXQUFVOzBCQUFnQjs7Ozs7Ozs7Ozs7SUFHbkM7SUFFQSxxQkFDRSw4REFBQ0Q7UUFBSUMsV0FBVTtrQkFDWkwsTUFBTU8sR0FBRyxDQUFDQyxDQUFBQSxxQkFDVCw4REFBQ1Ysb0RBQVdBO2dCQUFlVSxNQUFNQTtlQUFmQSxLQUFLQyxFQUFFOzs7Ozs7Ozs7O0FBSWpDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktY29udGVudC1hZ2dyZWdhdG9yLy4vc3JjL2NvbXBvbmVudHMvQ29udGVudExpc3QuanM/MTIyYiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgQ29udGVudENhcmQgZnJvbSAnLi9Db250ZW50Q2FyZCdcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQ29udGVudExpc3QoeyBpdGVtcyB9KSB7XG4gIC8vIENoZWNrIGlmIGl0ZW1zIGV4aXN0cyBhbmQgaXMgYW4gYXJyYXkgYmVmb3JlIGFjY2Vzc2luZyBsZW5ndGhcbiAgaWYgKCFpdGVtcyB8fCAhQXJyYXkuaXNBcnJheShpdGVtcykgfHwgaXRlbXMubGVuZ3RoID09PSAwKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktMTJcIj5cbiAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMFwiPk5vIGNvbnRlbnQgYXZhaWxhYmxlPC9wPlxuICAgICAgPC9kaXY+XG4gICAgKVxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgbGc6Z3JpZC1jb2xzLTMgZ2FwLTZcIj5cbiAgICAgIHtpdGVtcy5tYXAoaXRlbSA9PiAoXG4gICAgICAgIDxDb250ZW50Q2FyZCBrZXk9e2l0ZW0uaWR9IGl0ZW09e2l0ZW19IC8+XG4gICAgICApKX1cbiAgICA8L2Rpdj5cbiAgKVxufSJdLCJuYW1lcyI6WyJDb250ZW50Q2FyZCIsIkNvbnRlbnRMaXN0IiwiaXRlbXMiLCJBcnJheSIsImlzQXJyYXkiLCJsZW5ndGgiLCJkaXYiLCJjbGFzc05hbWUiLCJwIiwibWFwIiwiaXRlbSIsImlkIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/components/ContentList.js\n");

/***/ }),

/***/ "./src/components/DatePicker.js":
/*!**************************************!*\
  !*** ./src/components/DatePicker.js ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DatePicker)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction DatePicker({ selectedDate, onDateChange }) {\n    const [availableDates, setAvailableDates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        async function fetchDates() {\n            try {\n                setLoading(true);\n                const res = await fetch(\"/api/dates\");\n                const data = await res.json();\n                setAvailableDates(data);\n            } catch (error) {\n                console.error(\"Error fetching dates:\", error);\n            } finally{\n                setLoading(false);\n            }\n        }\n        fetchDates();\n    }, []);\n    const handleDateChange = (date)=>{\n        onDateChange(date);\n    };\n    const formatDate = (dateString)=>{\n        const date = new Date(dateString);\n        return date.toLocaleDateString(\"en-US\", {\n            weekday: \"short\",\n            month: \"short\",\n            day: \"numeric\"\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mb-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-lg font-semibold mb-2\",\n                children: \"Filter by Date\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Dashboard/src/components/DatePicker.js\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-sm text-gray-500\",\n                children: \"Loading dates...\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Dashboard/src/components/DatePicker.js\",\n                lineNumber: 42,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-wrap gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>handleDateChange(null),\n                        className: `px-3 py-1 rounded-full text-sm ${selectedDate === null ? \"bg-blue-500 text-white\" : \"bg-gray-200 hover:bg-gray-300\"}`,\n                        children: \"All Dates\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI Dashboard/src/components/DatePicker.js\",\n                        lineNumber: 45,\n                        columnNumber: 11\n                    }, this),\n                    availableDates.map((dateItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>handleDateChange(dateItem.date),\n                            className: `px-3 py-1 rounded-full text-sm ${selectedDate === dateItem.date ? \"bg-blue-500 text-white\" : \"bg-gray-200 hover:bg-gray-300\"}`,\n                            children: [\n                                formatDate(dateItem.date),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-1 text-xs\",\n                                    children: [\n                                        \"(\",\n                                        dateItem.count,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI Dashboard/src/components/DatePicker.js\",\n                                    lineNumber: 67,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, dateItem.date, true, {\n                            fileName: \"/Users/<USER>/Documents/AI Dashboard/src/components/DatePicker.js\",\n                            lineNumber: 57,\n                            columnNumber: 13\n                        }, this))\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/AI Dashboard/src/components/DatePicker.js\",\n                lineNumber: 44,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/AI Dashboard/src/components/DatePicker.js\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/DatePicker.js\n");

/***/ }),

/***/ "./src/pages/_app.js":
/*!***************************!*\
  !*** ./src/pages/_app.js ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"./src/styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction MyApp({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n        ...pageProps\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Dashboard/src/pages/_app.js\",\n        lineNumber: 4,\n        columnNumber: 10\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MyApp);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvX2FwcC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBOEI7QUFFOUIsU0FBU0EsTUFBTSxFQUFFQyxTQUFTLEVBQUVDLFNBQVMsRUFBRTtJQUNyQyxxQkFBTyw4REFBQ0Q7UUFBVyxHQUFHQyxTQUFTOzs7Ozs7QUFDakM7QUFFQSxpRUFBZUYsS0FBS0EsRUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLWNvbnRlbnQtYWdncmVnYXRvci8uL3NyYy9wYWdlcy9fYXBwLmpzPzhmZGEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICcuLi9zdHlsZXMvZ2xvYmFscy5jc3MnXG5cbmZ1bmN0aW9uIE15QXBwKHsgQ29tcG9uZW50LCBwYWdlUHJvcHMgfSkge1xuICByZXR1cm4gPENvbXBvbmVudCB7Li4ucGFnZVByb3BzfSAvPlxufVxuXG5leHBvcnQgZGVmYXVsdCBNeUFwcCJdLCJuYW1lcyI6WyJNeUFwcCIsIkNvbXBvbmVudCIsInBhZ2VQcm9wcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/pages/_app.js\n");

/***/ }),

/***/ "./src/pages/index.js":
/*!****************************!*\
  !*** ./src/pages/index.js ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ContentList__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/ContentList */ \"./src/components/ContentList.js\");\n/* harmony import */ var _components_CategoryTabs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/CategoryTabs */ \"./src/components/CategoryTabs.js\");\n/* harmony import */ var _components_DatePicker__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/DatePicker */ \"./src/components/DatePicker.js\");\n\n\n\n\n\n\nfunction Home() {\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeCategory, setActiveCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [selectedDate, setSelectedDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        async function fetchContent() {\n            try {\n                setLoading(true);\n                // Build URL with query parameters\n                let url = \"/api/content\";\n                const params = new URLSearchParams();\n                if (selectedDate) {\n                    params.append(\"date\", selectedDate);\n                }\n                if (activeCategory !== \"all\") {\n                    params.append(\"source\", activeCategory);\n                }\n                // Add params to URL if any exist\n                if (params.toString()) {\n                    url += `?${params.toString()}`;\n                }\n                const res = await fetch(url);\n                const data = await res.json();\n                setContent(data);\n            } catch (error) {\n                console.error(\"Error fetching content:\", error);\n            } finally{\n                setLoading(false);\n            }\n        }\n        fetchContent();\n    }, [\n        activeCategory,\n        selectedDate\n    ]);\n    const categories = [\n        {\n            id: \"all\",\n            name: \"All\"\n        },\n        {\n            id: \"twitter\",\n            name: \"Twitter\"\n        },\n        {\n            id: \"linkedin\",\n            name: \"LinkedIn\"\n        },\n        {\n            id: \"rss\",\n            name: \"News\"\n        }\n    ];\n    // Make sure content is an array before filtering\n    const filteredContent = Array.isArray(content) ? content : [];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"AI Content Aggregator\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI Dashboard/src/pages/index.js\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Daily curated AI content\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI Dashboard/src/pages/index.js\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI Dashboard/src/pages/index.js\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/AI Dashboard/src/pages/index.js\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold mb-2\",\n                        children: \"AI Content Aggregator\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI Dashboard/src/pages/index.js\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Your daily dose of AI news, research, and discussions\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI Dashboard/src/pages/index.js\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/AI Dashboard/src/pages/index.js\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col md:flex-row md:justify-between md:items-start gap-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CategoryTabs__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        categories: categories,\n                        activeCategory: activeCategory,\n                        setActiveCategory: setActiveCategory\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI Dashboard/src/pages/index.js\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DatePicker__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        selectedDate: selectedDate,\n                        onDateChange: setSelectedDate\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI Dashboard/src/pages/index.js\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/AI Dashboard/src/pages/index.js\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, this),\n            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"Loading content...\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI Dashboard/src/pages/index.js\",\n                    lineNumber: 88,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Dashboard/src/pages/index.js\",\n                lineNumber: 87,\n                columnNumber: 9\n            }, this) : filteredContent.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ContentList__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                items: filteredContent\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Dashboard/src/pages/index.js\",\n                lineNumber: 91,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500\",\n                        children: \"No content available for the selected filters\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI Dashboard/src/pages/index.js\",\n                        lineNumber: 94,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>{\n                            setSelectedDate(null);\n                            setActiveCategory(\"all\");\n                        },\n                        className: \"mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600\",\n                        children: \"Reset Filters\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI Dashboard/src/pages/index.js\",\n                        lineNumber: 95,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/AI Dashboard/src/pages/index.js\",\n                lineNumber: 93,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/AI Dashboard/src/pages/index.js\",\n        lineNumber: 59,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/index.js\n");

/***/ }),

/***/ "./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ (() => {



/***/ }),

/***/ "date-fns/formatDistanceToNow":
/*!***********************************************!*\
  !*** external "date-fns/formatDistanceToNow" ***!
  \***********************************************/
/***/ ((module) => {

"use strict";
module.exports = require("date-fns/formatDistanceToNow");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/head");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Findex.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();