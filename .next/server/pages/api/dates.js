"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/dates";
exports.ids = ["pages/api/dates"];
exports.modules = {

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

module.exports = require("child_process");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fdates&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Fapi%2Fdates.js&middlewareConfigBase64=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fdates&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Fapi%2Fdates.js&middlewareConfigBase64=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_dates_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/pages/api/dates.js */ \"(api)/./src/pages/api/dates.js\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_dates_js__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_dates_js__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/dates\",\n        pathname: \"/api/dates\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _src_pages_api_dates_js__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LXJvdXRlLWxvYWRlci9pbmRleC5qcz9raW5kPVBBR0VTX0FQSSZwYWdlPSUyRmFwaSUyRmRhdGVzJnByZWZlcnJlZFJlZ2lvbj0mYWJzb2x1dGVQYWdlUGF0aD0uJTJGc3JjJTJGcGFnZXMlMkZhcGklMkZkYXRlcy5qcyZtaWRkbGV3YXJlQ29uZmlnQmFzZTY0PWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBc0c7QUFDdkM7QUFDTDtBQUMxRDtBQUNxRDtBQUNyRDtBQUNBLGlFQUFlLHdFQUFLLENBQUMsb0RBQVEsWUFBWSxFQUFDO0FBQzFDO0FBQ08sZUFBZSx3RUFBSyxDQUFDLG9EQUFRO0FBQ3BDO0FBQ08sd0JBQXdCLGdIQUFtQjtBQUNsRDtBQUNBLGNBQWMseUVBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCxZQUFZO0FBQ1osQ0FBQzs7QUFFRCIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLWNvbnRlbnQtYWdncmVnYXRvci8/MzM0YyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQYWdlc0FQSVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLW1vZHVsZXMvcGFnZXMtYXBpL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLWtpbmRcIjtcbmltcG9ydCB7IGhvaXN0IH0gZnJvbSBcIm5leHQvZGlzdC9idWlsZC90ZW1wbGF0ZXMvaGVscGVyc1wiO1xuLy8gSW1wb3J0IHRoZSB1c2VybGFuZCBjb2RlLlxuaW1wb3J0ICogYXMgdXNlcmxhbmQgZnJvbSBcIi4vc3JjL3BhZ2VzL2FwaS9kYXRlcy5qc1wiO1xuLy8gUmUtZXhwb3J0IHRoZSBoYW5kbGVyIChzaG91bGQgYmUgdGhlIGRlZmF1bHQgZXhwb3J0KS5cbmV4cG9ydCBkZWZhdWx0IGhvaXN0KHVzZXJsYW5kLCBcImRlZmF1bHRcIik7XG4vLyBSZS1leHBvcnQgY29uZmlnLlxuZXhwb3J0IGNvbnN0IGNvbmZpZyA9IGhvaXN0KHVzZXJsYW5kLCBcImNvbmZpZ1wiKTtcbi8vIENyZWF0ZSBhbmQgZXhwb3J0IHRoZSByb3V0ZSBtb2R1bGUgdGhhdCB3aWxsIGJlIGNvbnN1bWVkLlxuZXhwb3J0IGNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IFBhZ2VzQVBJUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLlBBR0VTX0FQSSxcbiAgICAgICAgcGFnZTogXCIvYXBpL2RhdGVzXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9hcGkvZGF0ZXNcIixcbiAgICAgICAgLy8gVGhlIGZvbGxvd2luZyBhcmVuJ3QgdXNlZCBpbiBwcm9kdWN0aW9uLlxuICAgICAgICBidW5kbGVQYXRoOiBcIlwiLFxuICAgICAgICBmaWxlbmFtZTogXCJcIlxuICAgIH0sXG4gICAgdXNlcmxhbmRcbn0pO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1wYWdlcy1hcGkuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fdates&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Fapi%2Fdates.js&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/pages/api/dates.js":
/*!********************************!*\
  !*** ./src/pages/api/dates.js ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var child_process__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! child_process */ \"child_process\");\n/* harmony import */ var child_process__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(child_process__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var util__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! util */ \"util\");\n/* harmony import */ var util__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(util__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst execPromise = (0,util__WEBPACK_IMPORTED_MODULE_1__.promisify)(child_process__WEBPACK_IMPORTED_MODULE_0__.exec);\n// Mock dates as fallback if database connection fails\nconst mockDates = [\n    {\n        date: new Date().toISOString().split(\"T\")[0],\n        count: 6\n    },\n    {\n        date: new Date(Date.now() - 86400000).toISOString().split(\"T\")[0],\n        count: 4\n    },\n    {\n        date: new Date(Date.now() - 86400000 * 2).toISOString().split(\"T\")[0],\n        count: 8\n    },\n    {\n        date: new Date(Date.now() - 86400000 * 3).toISOString().split(\"T\")[0],\n        count: 5\n    },\n    {\n        date: new Date(Date.now() - 86400000 * 4).toISOString().split(\"T\")[0],\n        count: 3\n    },\n    {\n        date: new Date(Date.now() - 86400000 * 5).toISOString().split(\"T\")[0],\n        count: 7\n    },\n    {\n        date: new Date(Date.now() - 86400000 * 6).toISOString().split(\"T\")[0],\n        count: 2\n    }\n];\nasync function handler(_, res) {\n    try {\n        // Try to execute Python script to fetch available dates from database\n        const { stdout } = await execPromise(\"python src/api/get_content.py --get-dates\");\n        const dates = JSON.parse(stdout);\n        // Return real data from database\n        res.status(200).json(dates);\n    } catch (error) {\n        console.error(\"Error fetching dates from database:\", error);\n        console.log(\"Falling back to mock dates\");\n        // Fall back to mock dates if database connection fails\n        res.status(200).json(mockDates);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/dates.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fdates&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Fapi%2Fdates.js&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();