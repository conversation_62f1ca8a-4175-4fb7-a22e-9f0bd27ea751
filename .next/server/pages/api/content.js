"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/content";
exports.ids = ["pages/api/content"];
exports.modules = {

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

module.exports = require("child_process");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fcontent&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Fapi%2Fcontent.js&middlewareConfigBase64=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fcontent&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Fapi%2Fcontent.js&middlewareConfigBase64=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_content_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/pages/api/content.js */ \"(api)/./src/pages/api/content.js\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_content_js__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_content_js__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/content\",\n        pathname: \"/api/content\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _src_pages_api_content_js__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LXJvdXRlLWxvYWRlci9pbmRleC5qcz9raW5kPVBBR0VTX0FQSSZwYWdlPSUyRmFwaSUyRmNvbnRlbnQmcHJlZmVycmVkUmVnaW9uPSZhYnNvbHV0ZVBhZ2VQYXRoPS4lMkZzcmMlMkZwYWdlcyUyRmFwaSUyRmNvbnRlbnQuanMmbWlkZGxld2FyZUNvbmZpZ0Jhc2U2ND1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQXNHO0FBQ3ZDO0FBQ0w7QUFDMUQ7QUFDdUQ7QUFDdkQ7QUFDQSxpRUFBZSx3RUFBSyxDQUFDLHNEQUFRLFlBQVksRUFBQztBQUMxQztBQUNPLGVBQWUsd0VBQUssQ0FBQyxzREFBUTtBQUNwQztBQUNPLHdCQUF3QixnSEFBbUI7QUFDbEQ7QUFDQSxjQUFjLHlFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsWUFBWTtBQUNaLENBQUM7O0FBRUQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1jb250ZW50LWFnZ3JlZ2F0b3IvPzY5NWEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUGFnZXNBUElSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1tb2R1bGVzL3BhZ2VzLWFwaS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1raW5kXCI7XG5pbXBvcnQgeyBob2lzdCB9IGZyb20gXCJuZXh0L2Rpc3QvYnVpbGQvdGVtcGxhdGVzL2hlbHBlcnNcIjtcbi8vIEltcG9ydCB0aGUgdXNlcmxhbmQgY29kZS5cbmltcG9ydCAqIGFzIHVzZXJsYW5kIGZyb20gXCIuL3NyYy9wYWdlcy9hcGkvY29udGVudC5qc1wiO1xuLy8gUmUtZXhwb3J0IHRoZSBoYW5kbGVyIChzaG91bGQgYmUgdGhlIGRlZmF1bHQgZXhwb3J0KS5cbmV4cG9ydCBkZWZhdWx0IGhvaXN0KHVzZXJsYW5kLCBcImRlZmF1bHRcIik7XG4vLyBSZS1leHBvcnQgY29uZmlnLlxuZXhwb3J0IGNvbnN0IGNvbmZpZyA9IGhvaXN0KHVzZXJsYW5kLCBcImNvbmZpZ1wiKTtcbi8vIENyZWF0ZSBhbmQgZXhwb3J0IHRoZSByb3V0ZSBtb2R1bGUgdGhhdCB3aWxsIGJlIGNvbnN1bWVkLlxuZXhwb3J0IGNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IFBhZ2VzQVBJUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLlBBR0VTX0FQSSxcbiAgICAgICAgcGFnZTogXCIvYXBpL2NvbnRlbnRcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FwaS9jb250ZW50XCIsXG4gICAgICAgIC8vIFRoZSBmb2xsb3dpbmcgYXJlbid0IHVzZWQgaW4gcHJvZHVjdGlvbi5cbiAgICAgICAgYnVuZGxlUGF0aDogXCJcIixcbiAgICAgICAgZmlsZW5hbWU6IFwiXCJcbiAgICB9LFxuICAgIHVzZXJsYW5kXG59KTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cGFnZXMtYXBpLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fcontent&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Fapi%2Fcontent.js&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/pages/api/content.js":
/*!**********************************!*\
  !*** ./src/pages/api/content.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var child_process__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! child_process */ \"child_process\");\n/* harmony import */ var child_process__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(child_process__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var util__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! util */ \"util\");\n/* harmony import */ var util__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(util__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst execPromise = (0,util__WEBPACK_IMPORTED_MODULE_1__.promisify)(child_process__WEBPACK_IMPORTED_MODULE_0__.exec);\n// Mock data as fallback if database connection fails\nconst mockContent = [\n    {\n        id: \"1\",\n        title: \"The Future of AI in Healthcare\",\n        content: \"AI is revolutionizing healthcare with predictive analytics and personalized medicine...\",\n        url: \"https://example.com/ai-healthcare\",\n        source: \"twitter\",\n        published_at: new Date().toISOString(),\n        likes: 120,\n        shares: 45,\n        comments: 23\n    },\n    {\n        id: \"2\",\n        title: \"Machine Learning Trends in 2025\",\n        content: \"The top machine learning trends to watch for in 2025 include...\",\n        url: \"https://example.com/ml-trends\",\n        source: \"linkedin\",\n        published_at: new Date().toISOString(),\n        likes: 89,\n        shares: 34,\n        comments: 12\n    },\n    {\n        id: \"3\",\n        title: \"New Research in Natural Language Processing\",\n        content: \"Researchers have made significant breakthroughs in NLP...\",\n        url: \"https://example.com/nlp-research\",\n        source: \"rss\",\n        published_at: new Date().toISOString(),\n        likes: 56,\n        shares: 23,\n        comments: 8\n    },\n    {\n        id: \"4\",\n        title: \"Ethics in Artificial Intelligence\",\n        content: \"The ethical considerations of AI development are becoming increasingly important...\",\n        url: \"https://example.com/ai-ethics\",\n        source: \"twitter\",\n        published_at: new Date().toISOString(),\n        likes: 210,\n        shares: 78,\n        comments: 45\n    },\n    {\n        id: \"5\",\n        title: \"How Companies are Implementing AI\",\n        content: \"Leading companies are implementing AI in these innovative ways...\",\n        url: \"https://example.com/ai-implementation\",\n        source: \"linkedin\",\n        published_at: new Date().toISOString(),\n        likes: 145,\n        shares: 67,\n        comments: 32\n    },\n    {\n        id: \"6\",\n        title: \"The Role of Deep Learning in Computer Vision\",\n        content: \"Deep learning has transformed computer vision applications...\",\n        url: \"https://example.com/deep-learning-vision\",\n        source: \"rss\",\n        published_at: new Date().toISOString(),\n        likes: 78,\n        shares: 34,\n        comments: 15\n    }\n];\nasync function handler(req, res) {\n    try {\n        // Get query parameters\n        const { date, source, limit } = req.query;\n        // Build command with optional parameters\n        let command = \"python src/api/get_content.py\";\n        if (date) command += ` --date ${date}`;\n        if (source) command += ` --source ${source}`;\n        if (limit) command += ` --limit ${limit}`;\n        // Try to execute Python script to fetch content from database\n        const { stdout } = await execPromise(command);\n        const content = JSON.parse(stdout);\n        // Return real data from database\n        res.status(200).json(content);\n    } catch (error) {\n        console.error(\"Error fetching content from database:\", error);\n        console.log(\"Falling back to mock data\");\n        // Fall back to mock data if database connection fails\n        res.status(200).json(mockContent);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/content.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fcontent&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Fapi%2Fcontent.js&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();