"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/content";
exports.ids = ["pages/api/content"];
exports.modules = {

/***/ "mssql":
/*!************************!*\
  !*** external "mssql" ***!
  \************************/
/***/ ((module) => {

module.exports = require("mssql");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fcontent&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Fapi%2Fcontent.js&middlewareConfigBase64=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fcontent&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Fapi%2Fcontent.js&middlewareConfigBase64=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_content_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/pages/api/content.js */ \"(api)/./src/pages/api/content.js\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_content_js__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_content_js__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/content\",\n        pathname: \"/api/content\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _src_pages_api_content_js__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LXJvdXRlLWxvYWRlci9pbmRleC5qcz9raW5kPVBBR0VTX0FQSSZwYWdlPSUyRmFwaSUyRmNvbnRlbnQmcHJlZmVycmVkUmVnaW9uPSZhYnNvbHV0ZVBhZ2VQYXRoPS4lMkZzcmMlMkZwYWdlcyUyRmFwaSUyRmNvbnRlbnQuanMmbWlkZGxld2FyZUNvbmZpZ0Jhc2U2ND1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQXNHO0FBQ3ZDO0FBQ0w7QUFDMUQ7QUFDdUQ7QUFDdkQ7QUFDQSxpRUFBZSx3RUFBSyxDQUFDLHNEQUFRLFlBQVksRUFBQztBQUMxQztBQUNPLGVBQWUsd0VBQUssQ0FBQyxzREFBUTtBQUNwQztBQUNPLHdCQUF3QixnSEFBbUI7QUFDbEQ7QUFDQSxjQUFjLHlFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsWUFBWTtBQUNaLENBQUM7O0FBRUQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1jb250ZW50LWFnZ3JlZ2F0b3IvP2IxOTIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUGFnZXNBUElSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1tb2R1bGVzL3BhZ2VzLWFwaS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1raW5kXCI7XG5pbXBvcnQgeyBob2lzdCB9IGZyb20gXCJuZXh0L2Rpc3QvYnVpbGQvdGVtcGxhdGVzL2hlbHBlcnNcIjtcbi8vIEltcG9ydCB0aGUgdXNlcmxhbmQgY29kZS5cbmltcG9ydCAqIGFzIHVzZXJsYW5kIGZyb20gXCIuL3NyYy9wYWdlcy9hcGkvY29udGVudC5qc1wiO1xuLy8gUmUtZXhwb3J0IHRoZSBoYW5kbGVyIChzaG91bGQgYmUgdGhlIGRlZmF1bHQgZXhwb3J0KS5cbmV4cG9ydCBkZWZhdWx0IGhvaXN0KHVzZXJsYW5kLCBcImRlZmF1bHRcIik7XG4vLyBSZS1leHBvcnQgY29uZmlnLlxuZXhwb3J0IGNvbnN0IGNvbmZpZyA9IGhvaXN0KHVzZXJsYW5kLCBcImNvbmZpZ1wiKTtcbi8vIENyZWF0ZSBhbmQgZXhwb3J0IHRoZSByb3V0ZSBtb2R1bGUgdGhhdCB3aWxsIGJlIGNvbnN1bWVkLlxuZXhwb3J0IGNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IFBhZ2VzQVBJUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLlBBR0VTX0FQSSxcbiAgICAgICAgcGFnZTogXCIvYXBpL2NvbnRlbnRcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FwaS9jb250ZW50XCIsXG4gICAgICAgIC8vIFRoZSBmb2xsb3dpbmcgYXJlbid0IHVzZWQgaW4gcHJvZHVjdGlvbi5cbiAgICAgICAgYnVuZGxlUGF0aDogXCJcIixcbiAgICAgICAgZmlsZW5hbWU6IFwiXCJcbiAgICB9LFxuICAgIHVzZXJsYW5kXG59KTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cGFnZXMtYXBpLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fcontent&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Fapi%2Fcontent.js&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/lib/contentStorage.js":
/*!***********************************!*\
  !*** ./src/lib/contentStorage.js ***!
  \***********************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * Content storage module for the AI Dashboard.\n * Node.js version using mssql package.\n */ \nconst { executeQuery, sql } = __webpack_require__(/*! ./database */ \"(api)/./src/lib/database.js\");\nclass ContentStorage {\n    /**\n   * Get recent content from the database\n   * @param {number} limit - Maximum number of items to retrieve\n   * @param {string} source - Optional source filter\n   * @param {string} date - Optional date filter in ISO format (YYYY-MM-DD)\n   * @returns {Promise<Array>} List of content items\n   */ static async getRecentContent(limit = 50, source = null, date = null) {\n        try {\n            let query = `\n        SELECT TOP (@limit)\n          id,\n          title,\n          content,\n          url,\n          source,\n          published_at,\n          author_name,\n          likes,\n          shares,\n          comments,\n          summary\n        FROM content\n      `;\n            const params = {\n                limit\n            };\n            const conditions = [];\n            if (source) {\n                conditions.push(\"source = @source\");\n                params.source = source;\n            }\n            if (date) {\n                conditions.push(\"CONVERT(DATE, published_at) = @date\");\n                params.date = date;\n            }\n            if (conditions.length > 0) {\n                query += \" WHERE \" + conditions.join(\" AND \");\n            }\n            query += \" ORDER BY published_at DESC\";\n            const result = await executeQuery(query, params);\n            // Convert to the expected format\n            return result.recordset.map((item)=>({\n                    id: item.id,\n                    title: item.title,\n                    content: item.content,\n                    url: item.url,\n                    source: item.source,\n                    published_at: item.published_at ? item.published_at.toISOString() : null,\n                    author_name: item.author_name,\n                    likes: item.likes || 0,\n                    shares: item.shares || 0,\n                    comments: item.comments || 0,\n                    summary: item.summary\n                }));\n        } catch (error) {\n            console.error(\"Error retrieving content from database:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get a list of dates that have content available\n   * @param {number} limit - Maximum number of dates to retrieve\n   * @returns {Promise<Array>} List of dates with content counts\n   */ static async getAvailableDates(limit = 30) {\n        try {\n            const query = `\n        SELECT TOP (@limit)\n          CONVERT(DATE, published_at) as date,\n          COUNT(id) as count\n        FROM content \n        GROUP BY CONVERT(DATE, published_at)\n        ORDER BY CONVERT(DATE, published_at) DESC\n      `;\n            const result = await executeQuery(query, {\n                limit\n            });\n            // Convert to the expected format\n            return result.recordset.map((row)=>({\n                    date: row.date ? row.date.toISOString().split(\"T\")[0] : null,\n                    count: row.count\n                }));\n        } catch (error) {\n            console.error(\"Error retrieving available dates:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Save Twitter data to the database\n   * @param {Array} tweets - List of tweet data\n   * @returns {Promise<number>} Number of tweets saved\n   */ static async saveTwitterData(tweets) {\n        let count = 0;\n        for (const tweet of tweets){\n            try {\n                // Check if tweet already exists\n                const existingQuery = `\n          SELECT id FROM content \n          WHERE source = 'twitter' AND source_id = @sourceId\n        `;\n                const existing = await executeQuery(existingQuery, {\n                    sourceId: String(tweet.id)\n                });\n                if (existing.recordset.length > 0) {\n                    console.log(`Tweet ${tweet.id} already exists in database`);\n                    continue;\n                }\n                // Insert new tweet\n                const insertQuery = `\n          INSERT INTO content (\n            title, content, url, source, source_id, published_at, \n            author_name, likes, shares, comments\n          ) VALUES (\n            @title, @content, @url, @source, @sourceId, @publishedAt,\n            @authorName, @likes, @shares, @comments\n          )\n        `;\n                await executeQuery(insertQuery, {\n                    title: tweet.text ? tweet.text.substring(0, 255) : \"\",\n                    content: tweet.text || \"\",\n                    url: tweet.url || \"\",\n                    source: \"twitter\",\n                    sourceId: String(tweet.id),\n                    publishedAt: tweet.created_at ? new Date(tweet.created_at) : new Date(),\n                    authorName: tweet.author?.name || tweet.user?.name || \"\",\n                    likes: tweet.public_metrics?.like_count || 0,\n                    shares: tweet.public_metrics?.retweet_count || 0,\n                    comments: tweet.public_metrics?.reply_count || 0\n                });\n                count++;\n            } catch (error) {\n                console.error(`Error saving tweet ${tweet.id}:`, error);\n            }\n        }\n        console.log(`Saved ${count} new tweets to database`);\n        return count;\n    }\n    /**\n   * Save LinkedIn data to the database\n   * @param {Array} posts - List of LinkedIn post data\n   * @returns {Promise<number>} Number of posts saved\n   */ static async saveLinkedInData(posts) {\n        let count = 0;\n        for (const post of posts){\n            try {\n                // Check if post already exists\n                const existingQuery = `\n          SELECT id FROM content \n          WHERE source = 'linkedin' AND source_id = @sourceId\n        `;\n                const existing = await executeQuery(existingQuery, {\n                    sourceId: String(post.id)\n                });\n                if (existing.recordset.length > 0) {\n                    console.log(`LinkedIn post ${post.id} already exists in database`);\n                    continue;\n                }\n                // Insert new post\n                const insertQuery = `\n          INSERT INTO content (\n            title, content, url, source, source_id, published_at, \n            author_name, likes, shares, comments\n          ) VALUES (\n            @title, @content, @url, @source, @sourceId, @publishedAt,\n            @authorName, @likes, @shares, @comments\n          )\n        `;\n                await executeQuery(insertQuery, {\n                    title: post.text ? post.text.substring(0, 255) : \"\",\n                    content: post.text || \"\",\n                    url: post.url || \"\",\n                    source: \"linkedin\",\n                    sourceId: String(post.id),\n                    publishedAt: post.created_at ? new Date(post.created_at) : new Date(),\n                    authorName: post.author?.name || \"\",\n                    likes: post.likes || 0,\n                    shares: post.shares || 0,\n                    comments: post.comments || 0\n                });\n                count++;\n            } catch (error) {\n                console.error(`Error saving LinkedIn post ${post.id}:`, error);\n            }\n        }\n        console.log(`Saved ${count} new LinkedIn posts to database`);\n        return count;\n    }\n    /**\n   * Save RSS data to the database\n   * @param {Array} entries - List of RSS entry data\n   * @returns {Promise<number>} Number of entries saved\n   */ static async saveRSSData(entries) {\n        let count = 0;\n        for (const entry of entries){\n            try {\n                // Check if entry already exists\n                const existingQuery = `\n          SELECT id FROM content \n          WHERE source = 'rss' AND source_id = @sourceId\n        `;\n                const existing = await executeQuery(existingQuery, {\n                    sourceId: String(entry.id)\n                });\n                if (existing.recordset.length > 0) {\n                    console.log(`RSS entry ${entry.id} already exists in database`);\n                    continue;\n                }\n                // Insert new entry\n                const insertQuery = `\n          INSERT INTO content (\n            title, content, url, source, source_id, published_at, \n            author_name, summary\n          ) VALUES (\n            @title, @content, @url, @source, @sourceId, @publishedAt,\n            @authorName, @summary\n          )\n        `;\n                await executeQuery(insertQuery, {\n                    title: entry.title || \"\",\n                    content: entry.description || entry.content || \"\",\n                    url: entry.link || entry.url || \"\",\n                    source: \"rss\",\n                    sourceId: String(entry.id),\n                    publishedAt: entry.published ? new Date(entry.published) : new Date(),\n                    authorName: entry.author || \"\",\n                    summary: entry.summary || \"\"\n                });\n                count++;\n            } catch (error) {\n                console.error(`Error saving RSS entry ${entry.id}:`, error);\n            }\n        }\n        console.log(`Saved ${count} new RSS entries to database`);\n        return count;\n    }\n}\nmodule.exports = ContentStorage;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/lib/contentStorage.js\n");

/***/ }),

/***/ "(api)/./src/lib/database.js":
/*!*****************************!*\
  !*** ./src/lib/database.js ***!
  \*****************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * Database connection module for the AI Dashboard.\n * Node.js version using mssql package.\n */ \nconst sql = __webpack_require__(/*! mssql */ \"mssql\");\n// Database configuration\nconst config = {\n    server: process.env.DB_HOST || \"localhost\",\n    port: parseInt(process.env.DB_PORT || \"1433\"),\n    database: process.env.DB_NAME || \"aicontent\",\n    user: process.env.DB_USER || \"dhairya_mac\",\n    password: process.env.DB_PASSWORD || \"q1w2e3r4t5!\",\n    options: {\n        encrypt: true,\n        trustServerCertificate: true,\n        enableArithAbort: true,\n        requestTimeout: 30000,\n        connectionTimeout: 30000\n    },\n    pool: {\n        max: 10,\n        min: 0,\n        idleTimeoutMillis: 30000\n    }\n};\nlet pool = null;\n/**\n * Get database connection pool\n * @returns {Promise<sql.ConnectionPool>}\n */ async function getPool() {\n    if (!pool) {\n        try {\n            pool = await sql.connect(config);\n            console.log(\"Database connection established successfully\");\n        } catch (error) {\n            console.error(\"Error connecting to database:\", error);\n            throw error;\n        }\n    }\n    return pool;\n}\n/**\n * Execute a SQL query\n * @param {string} query - SQL query string\n * @param {Object} params - Query parameters\n * @returns {Promise<Object>} Query result\n */ async function executeQuery(query, params = {}) {\n    try {\n        const pool = await getPool();\n        const request = pool.request();\n        // Add parameters to request\n        Object.keys(params).forEach((key)=>{\n            request.input(key, params[key]);\n        });\n        const result = await request.query(query);\n        return result;\n    } catch (error) {\n        console.error(\"Error executing query:\", error);\n        throw error;\n    }\n}\n/**\n * Close database connection\n */ async function closeConnection() {\n    if (pool) {\n        await pool.close();\n        pool = null;\n        console.log(\"Database connection closed\");\n    }\n}\nmodule.exports = {\n    getPool,\n    executeQuery,\n    closeConnection,\n    sql\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/lib/database.js\n");

/***/ }),

/***/ "(api)/./src/pages/api/content.js":
/*!**********************************!*\
  !*** ./src/pages/api/content.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _lib_contentStorage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../lib/contentStorage */ \"(api)/./src/lib/contentStorage.js\");\n/* harmony import */ var _lib_contentStorage__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_lib_contentStorage__WEBPACK_IMPORTED_MODULE_0__);\n\nasync function handler(req, res) {\n    try {\n        // Get query parameters\n        const { date, source, limit } = req.query;\n        // Parse limit parameter\n        const parsedLimit = limit ? parseInt(limit, 10) : 30;\n        // Fetch content from database using Node.js storage\n        const content = await _lib_contentStorage__WEBPACK_IMPORTED_MODULE_0___default().getRecentContent(parsedLimit, source || null, date || null);\n        // Return data from database\n        res.status(200).json(content);\n    } catch (error) {\n        console.error(\"Error fetching content from database:\", error);\n        // Return error response\n        res.status(500).json({\n            error: \"Failed to fetch content from database\",\n            message: error.message\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/content.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fcontent&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Fapi%2Fcontent.js&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();