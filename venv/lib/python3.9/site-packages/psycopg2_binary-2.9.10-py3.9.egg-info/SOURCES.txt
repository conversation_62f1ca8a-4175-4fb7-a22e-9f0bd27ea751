AUTHORS
INSTALL
LICENSE
MANIFEST.in
Makefile
NEWS
README.rst
setup.cfg
setup.py
doc/COPYING.LESSER
doc/Makefile
doc/README.rst
doc/SUCCESS
doc/pep-0249.txt
doc/requirements.txt
doc/src/Makefile
doc/src/advanced.rst
doc/src/conf.py
doc/src/connection.rst
doc/src/cursor.rst
doc/src/errorcodes.rst
doc/src/errors.rst
doc/src/extensions.rst
doc/src/extras.rst
doc/src/faq.rst
doc/src/index.rst
doc/src/install.rst
doc/src/license.rst
doc/src/module.rst
doc/src/news.rst
doc/src/pool.rst
doc/src/sql.rst
doc/src/tz.rst
doc/src/usage.rst
doc/src/_static/psycopg.css
doc/src/tools/make_sqlstate_docs.py
doc/src/tools/lib/dbapi_extension.py
doc/src/tools/lib/sql_role.py
doc/src/tools/lib/ticket_role.py
lib/__init__.py
lib/_ipaddress.py
lib/_json.py
lib/_range.py
lib/errorcodes.py
lib/errors.py
lib/extensions.py
lib/extras.py
lib/pool.py
lib/sql.py
lib/tz.py
psycopg/_psycopg.vc9.amd64.manifest
psycopg/_psycopg.vc9.x86.manifest
psycopg/adapter_asis.c
psycopg/adapter_asis.h
psycopg/adapter_binary.c
psycopg/adapter_binary.h
psycopg/adapter_datetime.c
psycopg/adapter_datetime.h
psycopg/adapter_list.c
psycopg/adapter_list.h
psycopg/adapter_pboolean.c
psycopg/adapter_pboolean.h
psycopg/adapter_pdecimal.c
psycopg/adapter_pdecimal.h
psycopg/adapter_pfloat.c
psycopg/adapter_pfloat.h
psycopg/adapter_pint.c
psycopg/adapter_pint.h
psycopg/adapter_qstring.c
psycopg/adapter_qstring.h
psycopg/aix_support.c
psycopg/aix_support.h
psycopg/bytes_format.c
psycopg/column.h
psycopg/column_type.c
psycopg/config.h
psycopg/connection.h
psycopg/connection_int.c
psycopg/connection_type.c
psycopg/conninfo.h
psycopg/conninfo_type.c
psycopg/cursor.h
psycopg/cursor_int.c
psycopg/cursor_type.c
psycopg/diagnostics.h
psycopg/diagnostics_type.c
psycopg/error.h
psycopg/error_type.c
psycopg/green.c
psycopg/green.h
psycopg/libpq_support.c
psycopg/libpq_support.h
psycopg/lobject.h
psycopg/lobject_int.c
psycopg/lobject_type.c
psycopg/microprotocols.c
psycopg/microprotocols.h
psycopg/microprotocols_proto.c
psycopg/microprotocols_proto.h
psycopg/notify.h
psycopg/notify_type.c
psycopg/pgtypes.h
psycopg/pqpath.c
psycopg/pqpath.h
psycopg/psycopg.h
psycopg/psycopgmodule.c
psycopg/python.h
psycopg/replication_connection.h
psycopg/replication_connection_type.c
psycopg/replication_cursor.h
psycopg/replication_cursor_type.c
psycopg/replication_message.h
psycopg/replication_message_type.c
psycopg/solaris_support.c
psycopg/solaris_support.h
psycopg/sqlstate_errors.h
psycopg/typecast.c
psycopg/typecast.h
psycopg/typecast_array.c
psycopg/typecast_basic.c
psycopg/typecast_binary.c
psycopg/typecast_binary.h
psycopg/typecast_builtins.c
psycopg/typecast_datetime.c
psycopg/utils.c
psycopg/utils.h
psycopg/win32_support.c
psycopg/win32_support.h
psycopg/xid.h
psycopg/xid_type.c
psycopg2_binary.egg-info/PKG-INFO
psycopg2_binary.egg-info/SOURCES.txt
psycopg2_binary.egg-info/dependency_links.txt
psycopg2_binary.egg-info/top_level.txt
scripts/make_errorcodes.py
scripts/make_errors.py
scripts/refcounter.py
scripts/build/appveyor.py
scripts/build/build_libpq.sh
scripts/build/build_macos_arm64.sh
scripts/build/build_sdist.sh
scripts/build/download_packages_appveyor.py
scripts/build/print_so_versions.sh
scripts/build/run_build_macos_arm64.sh
scripts/build/scaleway_m1.sh
scripts/build/strip_wheel.sh
scripts/build/wheel_linux_before_all.sh
scripts/build/wheel_macos_before_all.sh
tests/__init__.py
tests/dbapi20.py
tests/dbapi20_tpc.py
tests/test_async.py
tests/test_bugX000.py
tests/test_bug_gc.py
tests/test_cancel.py
tests/test_connection.py
tests/test_copy.py
tests/test_cursor.py
tests/test_dates.py
tests/test_errcodes.py
tests/test_errors.py
tests/test_extras_dictcursor.py
tests/test_fast_executemany.py
tests/test_green.py
tests/test_ipaddress.py
tests/test_lobject.py
tests/test_module.py
tests/test_notify.py
tests/test_psycopg2_dbapi20.py
tests/test_quote.py
tests/test_replication.py
tests/test_sql.py
tests/test_transaction.py
tests/test_types_basic.py
tests/test_types_extras.py
tests/test_with.py
tests/testconfig.py
tests/testutils.py