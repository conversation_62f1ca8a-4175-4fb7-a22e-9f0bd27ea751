2025-05-15 16:18:58,835 - linkedin_collector - INFO - LinkedIn collector initialized with lix service
2025-05-15 16:19:01,395 - linkedin_collector - INFO - Generating 10 simulated LinkedIn posts
2025-05-15 16:41:25,947 - linkedin_collector - INFO - LinkedIn collector initialized with lix service
2025-05-15 16:41:25,947 - linkedin_collector - INFO - Collecting LinkedIn posts for keyword: artificial intelligence
2025-05-15 16:41:27,653 - linkedin_collector - INFO - Collected 5 posts for keyword artificial intelligence
2025-05-15 16:41:27,867 - linkedin_collector - INFO - LinkedIn collector initialized with lix service
2025-05-15 16:41:30,405 - linkedin_collector - INFO - Collecting LinkedIn posts for keyword: openai
2025-05-15 16:41:32,410 - linkedin_collector - INFO - Collected 10 posts for keyword openai
2025-05-15 16:41:33,414 - linkedin_collector - INFO - Collecting LinkedIn posts for keyword: anthropic
2025-05-15 16:41:37,171 - linkedin_collector - INFO - Collected 10 posts for keyword anthropic
2025-05-15 16:41:38,176 - linkedin_collector - INFO - Collecting LinkedIn posts for keyword: google ai
2025-05-15 16:41:38,370 - linkedin_collector - ERROR - Error making request to lix: 402 Client Error: Payment Required for url: https://api.lix-it.com/v1/li/linkedin/search/posts?url=https%3A%2F%2Fwww.linkedin.com%2Fsearch%2Fresults%2Fcontent%2F%3Fkeywords%3Dgoogle%20ai%26origin%3DSWITCH_SEARCH_VERTICAL%26sid%3DV2J
2025-05-15 16:41:38,371 - linkedin_collector - WARNING - No response from Lix for keyword google ai
2025-05-15 16:41:39,375 - linkedin_collector - INFO - Collecting LinkedIn posts for keyword: meta ai
2025-05-15 16:41:39,570 - linkedin_collector - ERROR - Error making request to lix: 402 Client Error: Payment Required for url: https://api.lix-it.com/v1/li/linkedin/search/posts?url=https%3A%2F%2Fwww.linkedin.com%2Fsearch%2Fresults%2Fcontent%2F%3Fkeywords%3Dmeta%20ai%26origin%3DSWITCH_SEARCH_VERTICAL%26sid%3DV2J
2025-05-15 16:41:39,571 - linkedin_collector - WARNING - No response from Lix for keyword meta ai
2025-05-15 16:41:40,576 - linkedin_collector - INFO - Collecting LinkedIn posts for keyword: deepmind
2025-05-15 16:41:40,802 - linkedin_collector - ERROR - Error making request to lix: 402 Client Error: Payment Required for url: https://api.lix-it.com/v1/li/linkedin/search/posts?url=https%3A%2F%2Fwww.linkedin.com%2Fsearch%2Fresults%2Fcontent%2F%3Fkeywords%3Ddeepmind%26origin%3DSWITCH_SEARCH_VERTICAL%26sid%3DV2J
2025-05-15 16:41:40,803 - linkedin_collector - WARNING - No response from Lix for keyword deepmind
2025-05-15 16:41:41,805 - linkedin_collector - INFO - Collecting LinkedIn posts for keyword: stability ai
2025-05-15 16:41:41,994 - linkedin_collector - ERROR - Error making request to lix: 402 Client Error: Payment Required for url: https://api.lix-it.com/v1/li/linkedin/search/posts?url=https%3A%2F%2Fwww.linkedin.com%2Fsearch%2Fresults%2Fcontent%2F%3Fkeywords%3Dstability%20ai%26origin%3DSWITCH_SEARCH_VERTICAL%26sid%3DV2J
2025-05-15 16:41:41,995 - linkedin_collector - WARNING - No response from Lix for keyword stability ai
2025-05-15 16:41:43,000 - linkedin_collector - INFO - Collecting LinkedIn posts for keyword: midjourney
2025-05-15 16:41:43,197 - linkedin_collector - ERROR - Error making request to lix: 402 Client Error: Payment Required for url: https://api.lix-it.com/v1/li/linkedin/search/posts?url=https%3A%2F%2Fwww.linkedin.com%2Fsearch%2Fresults%2Fcontent%2F%3Fkeywords%3Dmidjourney%26origin%3DSWITCH_SEARCH_VERTICAL%26sid%3DV2J
2025-05-15 16:41:43,198 - linkedin_collector - WARNING - No response from Lix for keyword midjourney
2025-05-15 16:41:44,204 - linkedin_collector - INFO - Collecting LinkedIn posts for keyword: microsoft ai
2025-05-15 16:41:44,392 - linkedin_collector - ERROR - Error making request to lix: 402 Client Error: Payment Required for url: https://api.lix-it.com/v1/li/linkedin/search/posts?url=https%3A%2F%2Fwww.linkedin.com%2Fsearch%2Fresults%2Fcontent%2F%3Fkeywords%3Dmicrosoft%20ai%26origin%3DSWITCH_SEARCH_VERTICAL%26sid%3DV2J
2025-05-15 16:41:44,393 - linkedin_collector - WARNING - No response from Lix for keyword microsoft ai
2025-05-15 16:41:45,398 - linkedin_collector - INFO - Collecting LinkedIn posts for keyword: andrew ng
2025-05-15 16:41:45,588 - linkedin_collector - ERROR - Error making request to lix: 402 Client Error: Payment Required for url: https://api.lix-it.com/v1/li/linkedin/search/posts?url=https%3A%2F%2Fwww.linkedin.com%2Fsearch%2Fresults%2Fcontent%2F%3Fkeywords%3Dandrew%20ng%26origin%3DSWITCH_SEARCH_VERTICAL%26sid%3DV2J
2025-05-15 16:41:45,589 - linkedin_collector - WARNING - No response from Lix for keyword andrew ng
2025-05-15 16:41:46,593 - linkedin_collector - INFO - Collecting LinkedIn posts for keyword: yann lecun
2025-05-15 16:41:46,795 - linkedin_collector - ERROR - Error making request to lix: 402 Client Error: Payment Required for url: https://api.lix-it.com/v1/li/linkedin/search/posts?url=https%3A%2F%2Fwww.linkedin.com%2Fsearch%2Fresults%2Fcontent%2F%3Fkeywords%3Dyann%20lecun%26origin%3DSWITCH_SEARCH_VERTICAL%26sid%3DV2J
2025-05-15 16:41:46,796 - linkedin_collector - WARNING - No response from Lix for keyword yann lecun
2025-05-15 16:41:47,801 - linkedin_collector - INFO - Collecting LinkedIn posts for keyword: geoffrey hinton
2025-05-15 16:41:48,026 - linkedin_collector - ERROR - Error making request to lix: 402 Client Error: Payment Required for url: https://api.lix-it.com/v1/li/linkedin/search/posts?url=https%3A%2F%2Fwww.linkedin.com%2Fsearch%2Fresults%2Fcontent%2F%3Fkeywords%3Dgeoffrey%20hinton%26origin%3DSWITCH_SEARCH_VERTICAL%26sid%3DV2J
2025-05-15 16:41:48,027 - linkedin_collector - WARNING - No response from Lix for keyword geoffrey hinton
2025-05-15 16:41:49,033 - linkedin_collector - INFO - Collecting LinkedIn posts for keyword: fei-fei li
2025-05-15 16:41:49,220 - linkedin_collector - ERROR - Error making request to lix: 402 Client Error: Payment Required for url: https://api.lix-it.com/v1/li/linkedin/search/posts?url=https%3A%2F%2Fwww.linkedin.com%2Fsearch%2Fresults%2Fcontent%2F%3Fkeywords%3Dfei-fei%20li%26origin%3DSWITCH_SEARCH_VERTICAL%26sid%3DV2J
2025-05-15 16:41:49,221 - linkedin_collector - WARNING - No response from Lix for keyword fei-fei li
2025-05-15 16:41:50,226 - linkedin_collector - INFO - Collecting LinkedIn posts for keyword: demis hassabis
2025-05-15 16:41:50,451 - linkedin_collector - ERROR - Error making request to lix: 402 Client Error: Payment Required for url: https://api.lix-it.com/v1/li/linkedin/search/posts?url=https%3A%2F%2Fwww.linkedin.com%2Fsearch%2Fresults%2Fcontent%2F%3Fkeywords%3Ddemis%20hassabis%26origin%3DSWITCH_SEARCH_VERTICAL%26sid%3DV2J
2025-05-15 16:41:50,452 - linkedin_collector - WARNING - No response from Lix for keyword demis hassabis
2025-05-15 16:41:51,455 - linkedin_collector - INFO - Collecting LinkedIn posts for keyword: sam altman
2025-05-15 16:41:51,718 - linkedin_collector - ERROR - Error making request to lix: 402 Client Error: Payment Required for url: https://api.lix-it.com/v1/li/linkedin/search/posts?url=https%3A%2F%2Fwww.linkedin.com%2Fsearch%2Fresults%2Fcontent%2F%3Fkeywords%3Dsam%20altman%26origin%3DSWITCH_SEARCH_VERTICAL%26sid%3DV2J
2025-05-15 16:41:51,719 - linkedin_collector - WARNING - No response from Lix for keyword sam altman
2025-05-15 16:41:52,723 - linkedin_collector - INFO - Collecting LinkedIn posts for keyword: dario amodei
2025-05-15 16:41:52,903 - linkedin_collector - ERROR - Error making request to lix: 402 Client Error: Payment Required for url: https://api.lix-it.com/v1/li/linkedin/search/posts?url=https%3A%2F%2Fwww.linkedin.com%2Fsearch%2Fresults%2Fcontent%2F%3Fkeywords%3Ddario%20amodei%26origin%3DSWITCH_SEARCH_VERTICAL%26sid%3DV2J
2025-05-15 16:41:52,904 - linkedin_collector - WARNING - No response from Lix for keyword dario amodei
2025-05-15 16:41:53,908 - linkedin_collector - INFO - Collecting LinkedIn posts for keyword: artificial intelligence
2025-05-15 16:41:54,103 - linkedin_collector - ERROR - Error making request to lix: 402 Client Error: Payment Required for url: https://api.lix-it.com/v1/li/linkedin/search/posts?url=https%3A%2F%2Fwww.linkedin.com%2Fsearch%2Fresults%2Fcontent%2F%3Fkeywords%3Dartificial%20intelligence%26origin%3DSWITCH_SEARCH_VERTICAL%26sid%3DV2J
2025-05-15 16:41:54,104 - linkedin_collector - WARNING - No response from Lix for keyword artificial intelligence
2025-05-15 16:41:55,108 - linkedin_collector - INFO - Collecting LinkedIn posts for keyword: machine learning
2025-05-15 16:41:55,306 - linkedin_collector - ERROR - Error making request to lix: 402 Client Error: Payment Required for url: https://api.lix-it.com/v1/li/linkedin/search/posts?url=https%3A%2F%2Fwww.linkedin.com%2Fsearch%2Fresults%2Fcontent%2F%3Fkeywords%3Dmachine%20learning%26origin%3DSWITCH_SEARCH_VERTICAL%26sid%3DV2J
2025-05-15 16:41:55,311 - linkedin_collector - WARNING - No response from Lix for keyword machine learning
2025-05-15 16:41:56,312 - linkedin_collector - INFO - Collecting LinkedIn posts for keyword: deep learning
2025-05-15 16:41:56,506 - linkedin_collector - ERROR - Error making request to lix: 402 Client Error: Payment Required for url: https://api.lix-it.com/v1/li/linkedin/search/posts?url=https%3A%2F%2Fwww.linkedin.com%2Fsearch%2Fresults%2Fcontent%2F%3Fkeywords%3Ddeep%20learning%26origin%3DSWITCH_SEARCH_VERTICAL%26sid%3DV2J
2025-05-15 16:41:56,507 - linkedin_collector - WARNING - No response from Lix for keyword deep learning
2025-05-15 16:41:57,508 - linkedin_collector - INFO - Collecting LinkedIn posts for keyword: neural network
2025-05-15 16:41:57,705 - linkedin_collector - ERROR - Error making request to lix: 402 Client Error: Payment Required for url: https://api.lix-it.com/v1/li/linkedin/search/posts?url=https%3A%2F%2Fwww.linkedin.com%2Fsearch%2Fresults%2Fcontent%2F%3Fkeywords%3Dneural%20network%26origin%3DSWITCH_SEARCH_VERTICAL%26sid%3DV2J
2025-05-15 16:41:57,706 - linkedin_collector - WARNING - No response from Lix for keyword neural network
2025-05-15 16:41:58,710 - linkedin_collector - INFO - Collecting LinkedIn posts for keyword: llm
2025-05-15 16:41:58,925 - linkedin_collector - ERROR - Error making request to lix: 402 Client Error: Payment Required for url: https://api.lix-it.com/v1/li/linkedin/search/posts?url=https%3A%2F%2Fwww.linkedin.com%2Fsearch%2Fresults%2Fcontent%2F%3Fkeywords%3Dllm%26origin%3DSWITCH_SEARCH_VERTICAL%26sid%3DV2J
2025-05-15 16:41:58,926 - linkedin_collector - WARNING - No response from Lix for keyword llm
2025-05-15 16:41:59,927 - linkedin_collector - INFO - Collecting LinkedIn posts for keyword: large language model
2025-05-15 16:42:00,139 - linkedin_collector - ERROR - Error making request to lix: 402 Client Error: Payment Required for url: https://api.lix-it.com/v1/li/linkedin/search/posts?url=https%3A%2F%2Fwww.linkedin.com%2Fsearch%2Fresults%2Fcontent%2F%3Fkeywords%3Dlarge%20language%20model%26origin%3DSWITCH_SEARCH_VERTICAL%26sid%3DV2J
2025-05-15 16:42:00,140 - linkedin_collector - WARNING - No response from Lix for keyword large language model
2025-05-15 16:42:01,143 - linkedin_collector - INFO - Collecting LinkedIn posts for keyword: gpt
2025-05-15 16:42:01,362 - linkedin_collector - ERROR - Error making request to lix: 402 Client Error: Payment Required for url: https://api.lix-it.com/v1/li/linkedin/search/posts?url=https%3A%2F%2Fwww.linkedin.com%2Fsearch%2Fresults%2Fcontent%2F%3Fkeywords%3Dgpt%26origin%3DSWITCH_SEARCH_VERTICAL%26sid%3DV2J
2025-05-15 16:42:01,363 - linkedin_collector - WARNING - No response from Lix for keyword gpt
2025-05-15 16:42:02,364 - linkedin_collector - INFO - Collecting LinkedIn posts for keyword: generative ai
2025-05-15 16:42:02,620 - linkedin_collector - ERROR - Error making request to lix: 402 Client Error: Payment Required for url: https://api.lix-it.com/v1/li/linkedin/search/posts?url=https%3A%2F%2Fwww.linkedin.com%2Fsearch%2Fresults%2Fcontent%2F%3Fkeywords%3Dgenerative%20ai%26origin%3DSWITCH_SEARCH_VERTICAL%26sid%3DV2J
2025-05-15 16:42:02,621 - linkedin_collector - WARNING - No response from Lix for keyword generative ai
2025-05-15 16:42:03,623 - linkedin_collector - INFO - Collected 20 unique LinkedIn posts
2025-05-15 17:03:07,119 - linkedin_collector - INFO - LinkedIn collector initialized with lix service
2025-05-15 17:03:17,984 - linkedin_collector - INFO - Generating 10 simulated LinkedIn posts
2025-05-15 17:07:08,764 - linkedin_collector - INFO - LinkedIn collector initialized with lix service
2025-05-15 17:07:10,242 - linkedin_collector - INFO - Collecting LinkedIn posts for keyword: openai
2025-05-15 17:07:10,486 - linkedin_collector - ERROR - Error making request to lix: 402 Client Error: Payment Required for url: https://api.lix-it.com/v1/li/linkedin/search/posts?url=https%3A%2F%2Fwww.linkedin.com%2Fsearch%2Fresults%2Fcontent%2F%3Fkeywords%3Dopenai%26origin%3DSWITCH_SEARCH_VERTICAL%26sid%3DV2J
2025-05-15 17:07:10,487 - linkedin_collector - WARNING - No response from Lix for keyword openai
2025-05-15 17:07:11,491 - linkedin_collector - INFO - Collecting LinkedIn posts for keyword: anthropic
2025-05-15 17:07:11,694 - linkedin_collector - ERROR - Error making request to lix: 402 Client Error: Payment Required for url: https://api.lix-it.com/v1/li/linkedin/search/posts?url=https%3A%2F%2Fwww.linkedin.com%2Fsearch%2Fresults%2Fcontent%2F%3Fkeywords%3Danthropic%26origin%3DSWITCH_SEARCH_VERTICAL%26sid%3DV2J
2025-05-15 17:07:11,695 - linkedin_collector - WARNING - No response from Lix for keyword anthropic
2025-05-15 17:07:12,700 - linkedin_collector - INFO - Collecting LinkedIn posts for keyword: google ai
2025-05-15 17:07:12,895 - linkedin_collector - ERROR - Error making request to lix: 402 Client Error: Payment Required for url: https://api.lix-it.com/v1/li/linkedin/search/posts?url=https%3A%2F%2Fwww.linkedin.com%2Fsearch%2Fresults%2Fcontent%2F%3Fkeywords%3Dgoogle%20ai%26origin%3DSWITCH_SEARCH_VERTICAL%26sid%3DV2J
2025-05-15 17:07:12,896 - linkedin_collector - WARNING - No response from Lix for keyword google ai
2025-05-15 17:07:13,901 - linkedin_collector - INFO - Collecting LinkedIn posts for keyword: meta ai
2025-05-15 17:07:14,095 - linkedin_collector - ERROR - Error making request to lix: 402 Client Error: Payment Required for url: https://api.lix-it.com/v1/li/linkedin/search/posts?url=https%3A%2F%2Fwww.linkedin.com%2Fsearch%2Fresults%2Fcontent%2F%3Fkeywords%3Dmeta%20ai%26origin%3DSWITCH_SEARCH_VERTICAL%26sid%3DV2J
2025-05-15 17:07:14,096 - linkedin_collector - WARNING - No response from Lix for keyword meta ai
2025-05-15 17:07:15,100 - linkedin_collector - INFO - Collecting LinkedIn posts for keyword: deepmind
2025-05-15 17:07:15,328 - linkedin_collector - ERROR - Error making request to lix: 402 Client Error: Payment Required for url: https://api.lix-it.com/v1/li/linkedin/search/posts?url=https%3A%2F%2Fwww.linkedin.com%2Fsearch%2Fresults%2Fcontent%2F%3Fkeywords%3Ddeepmind%26origin%3DSWITCH_SEARCH_VERTICAL%26sid%3DV2J
2025-05-15 17:07:15,330 - linkedin_collector - WARNING - No response from Lix for keyword deepmind
2025-05-15 17:07:16,336 - linkedin_collector - INFO - Collecting LinkedIn posts for keyword: stability ai
2025-05-15 17:07:16,586 - linkedin_collector - ERROR - Error making request to lix: 402 Client Error: Payment Required for url: https://api.lix-it.com/v1/li/linkedin/search/posts?url=https%3A%2F%2Fwww.linkedin.com%2Fsearch%2Fresults%2Fcontent%2F%3Fkeywords%3Dstability%20ai%26origin%3DSWITCH_SEARCH_VERTICAL%26sid%3DV2J
2025-05-15 17:07:16,587 - linkedin_collector - WARNING - No response from Lix for keyword stability ai
2025-05-15 17:07:17,608 - linkedin_collector - INFO - Collecting LinkedIn posts for keyword: midjourney
2025-05-15 17:07:17,810 - linkedin_collector - ERROR - Error making request to lix: 402 Client Error: Payment Required for url: https://api.lix-it.com/v1/li/linkedin/search/posts?url=https%3A%2F%2Fwww.linkedin.com%2Fsearch%2Fresults%2Fcontent%2F%3Fkeywords%3Dmidjourney%26origin%3DSWITCH_SEARCH_VERTICAL%26sid%3DV2J
2025-05-15 17:07:17,811 - linkedin_collector - WARNING - No response from Lix for keyword midjourney
2025-05-15 17:07:18,817 - linkedin_collector - INFO - Collecting LinkedIn posts for keyword: microsoft ai
2025-05-15 17:07:19,019 - linkedin_collector - ERROR - Error making request to lix: 402 Client Error: Payment Required for url: https://api.lix-it.com/v1/li/linkedin/search/posts?url=https%3A%2F%2Fwww.linkedin.com%2Fsearch%2Fresults%2Fcontent%2F%3Fkeywords%3Dmicrosoft%20ai%26origin%3DSWITCH_SEARCH_VERTICAL%26sid%3DV2J
2025-05-15 17:07:19,020 - linkedin_collector - WARNING - No response from Lix for keyword microsoft ai
2025-05-15 17:07:20,025 - linkedin_collector - INFO - Collecting LinkedIn posts for keyword: andrew ng
2025-05-15 17:07:20,315 - linkedin_collector - ERROR - Error making request to lix: 402 Client Error: Payment Required for url: https://api.lix-it.com/v1/li/linkedin/search/posts?url=https%3A%2F%2Fwww.linkedin.com%2Fsearch%2Fresults%2Fcontent%2F%3Fkeywords%3Dandrew%20ng%26origin%3DSWITCH_SEARCH_VERTICAL%26sid%3DV2J
2025-05-15 17:07:20,316 - linkedin_collector - WARNING - No response from Lix for keyword andrew ng
2025-05-15 17:07:21,322 - linkedin_collector - INFO - Collecting LinkedIn posts for keyword: yann lecun
2025-05-15 17:07:21,665 - linkedin_collector - ERROR - Error making request to lix: 402 Client Error: Payment Required for url: https://api.lix-it.com/v1/li/linkedin/search/posts?url=https%3A%2F%2Fwww.linkedin.com%2Fsearch%2Fresults%2Fcontent%2F%3Fkeywords%3Dyann%20lecun%26origin%3DSWITCH_SEARCH_VERTICAL%26sid%3DV2J
2025-05-15 17:07:22,034 - linkedin_collector - WARNING - No response from Lix for keyword yann lecun
2025-05-15 17:07:23,221 - linkedin_collector - INFO - Collecting LinkedIn posts for keyword: geoffrey hinton
2025-05-15 17:07:23,420 - linkedin_collector - ERROR - Error making request to lix: 402 Client Error: Payment Required for url: https://api.lix-it.com/v1/li/linkedin/search/posts?url=https%3A%2F%2Fwww.linkedin.com%2Fsearch%2Fresults%2Fcontent%2F%3Fkeywords%3Dgeoffrey%20hinton%26origin%3DSWITCH_SEARCH_VERTICAL%26sid%3DV2J
2025-05-15 17:07:23,421 - linkedin_collector - WARNING - No response from Lix for keyword geoffrey hinton
2025-05-15 17:07:24,423 - linkedin_collector - INFO - Collecting LinkedIn posts for keyword: fei-fei li
2025-05-15 17:07:24,621 - linkedin_collector - ERROR - Error making request to lix: 402 Client Error: Payment Required for url: https://api.lix-it.com/v1/li/linkedin/search/posts?url=https%3A%2F%2Fwww.linkedin.com%2Fsearch%2Fresults%2Fcontent%2F%3Fkeywords%3Dfei-fei%20li%26origin%3DSWITCH_SEARCH_VERTICAL%26sid%3DV2J
2025-05-15 17:07:24,622 - linkedin_collector - WARNING - No response from Lix for keyword fei-fei li
2025-05-15 17:07:25,627 - linkedin_collector - INFO - Collecting LinkedIn posts for keyword: demis hassabis
2025-05-15 17:07:25,824 - linkedin_collector - ERROR - Error making request to lix: 402 Client Error: Payment Required for url: https://api.lix-it.com/v1/li/linkedin/search/posts?url=https%3A%2F%2Fwww.linkedin.com%2Fsearch%2Fresults%2Fcontent%2F%3Fkeywords%3Ddemis%20hassabis%26origin%3DSWITCH_SEARCH_VERTICAL%26sid%3DV2J
2025-05-15 17:07:25,825 - linkedin_collector - WARNING - No response from Lix for keyword demis hassabis
2025-05-15 17:07:26,829 - linkedin_collector - INFO - Collecting LinkedIn posts for keyword: sam altman
2025-05-15 17:07:27,035 - linkedin_collector - ERROR - Error making request to lix: 402 Client Error: Payment Required for url: https://api.lix-it.com/v1/li/linkedin/search/posts?url=https%3A%2F%2Fwww.linkedin.com%2Fsearch%2Fresults%2Fcontent%2F%3Fkeywords%3Dsam%20altman%26origin%3DSWITCH_SEARCH_VERTICAL%26sid%3DV2J
2025-05-15 17:07:27,035 - linkedin_collector - WARNING - No response from Lix for keyword sam altman
2025-05-15 17:07:28,041 - linkedin_collector - INFO - Collecting LinkedIn posts for keyword: dario amodei
2025-05-15 17:07:28,252 - linkedin_collector - ERROR - Error making request to lix: 402 Client Error: Payment Required for url: https://api.lix-it.com/v1/li/linkedin/search/posts?url=https%3A%2F%2Fwww.linkedin.com%2Fsearch%2Fresults%2Fcontent%2F%3Fkeywords%3Ddario%20amodei%26origin%3DSWITCH_SEARCH_VERTICAL%26sid%3DV2J
2025-05-15 17:07:28,253 - linkedin_collector - WARNING - No response from Lix for keyword dario amodei
2025-05-15 17:07:29,258 - linkedin_collector - INFO - Collecting LinkedIn posts for keyword: artificial intelligence
2025-05-15 17:07:29,481 - linkedin_collector - ERROR - Error making request to lix: 402 Client Error: Payment Required for url: https://api.lix-it.com/v1/li/linkedin/search/posts?url=https%3A%2F%2Fwww.linkedin.com%2Fsearch%2Fresults%2Fcontent%2F%3Fkeywords%3Dartificial%20intelligence%26origin%3DSWITCH_SEARCH_VERTICAL%26sid%3DV2J
2025-05-15 17:07:29,482 - linkedin_collector - WARNING - No response from Lix for keyword artificial intelligence
2025-05-15 17:07:30,483 - linkedin_collector - INFO - Collecting LinkedIn posts for keyword: machine learning
2025-05-15 17:07:30,677 - linkedin_collector - ERROR - Error making request to lix: 402 Client Error: Payment Required for url: https://api.lix-it.com/v1/li/linkedin/search/posts?url=https%3A%2F%2Fwww.linkedin.com%2Fsearch%2Fresults%2Fcontent%2F%3Fkeywords%3Dmachine%20learning%26origin%3DSWITCH_SEARCH_VERTICAL%26sid%3DV2J
2025-05-15 17:07:30,679 - linkedin_collector - WARNING - No response from Lix for keyword machine learning
2025-05-15 17:07:31,685 - linkedin_collector - INFO - Collecting LinkedIn posts for keyword: deep learning
2025-05-15 17:07:31,888 - linkedin_collector - ERROR - Error making request to lix: 402 Client Error: Payment Required for url: https://api.lix-it.com/v1/li/linkedin/search/posts?url=https%3A%2F%2Fwww.linkedin.com%2Fsearch%2Fresults%2Fcontent%2F%3Fkeywords%3Ddeep%20learning%26origin%3DSWITCH_SEARCH_VERTICAL%26sid%3DV2J
2025-05-15 17:07:31,888 - linkedin_collector - WARNING - No response from Lix for keyword deep learning
2025-05-15 17:07:32,894 - linkedin_collector - INFO - Collecting LinkedIn posts for keyword: neural network
2025-05-15 17:07:33,096 - linkedin_collector - ERROR - Error making request to lix: 402 Client Error: Payment Required for url: https://api.lix-it.com/v1/li/linkedin/search/posts?url=https%3A%2F%2Fwww.linkedin.com%2Fsearch%2Fresults%2Fcontent%2F%3Fkeywords%3Dneural%20network%26origin%3DSWITCH_SEARCH_VERTICAL%26sid%3DV2J
2025-05-15 17:07:33,097 - linkedin_collector - WARNING - No response from Lix for keyword neural network
2025-05-15 17:07:34,102 - linkedin_collector - INFO - Collecting LinkedIn posts for keyword: llm
2025-05-15 17:07:34,291 - linkedin_collector - ERROR - Error making request to lix: 402 Client Error: Payment Required for url: https://api.lix-it.com/v1/li/linkedin/search/posts?url=https%3A%2F%2Fwww.linkedin.com%2Fsearch%2Fresults%2Fcontent%2F%3Fkeywords%3Dllm%26origin%3DSWITCH_SEARCH_VERTICAL%26sid%3DV2J
2025-05-15 17:07:34,292 - linkedin_collector - WARNING - No response from Lix for keyword llm
2025-05-15 17:07:35,297 - linkedin_collector - INFO - Collecting LinkedIn posts for keyword: large language model
2025-05-15 17:07:35,516 - linkedin_collector - ERROR - Error making request to lix: 402 Client Error: Payment Required for url: https://api.lix-it.com/v1/li/linkedin/search/posts?url=https%3A%2F%2Fwww.linkedin.com%2Fsearch%2Fresults%2Fcontent%2F%3Fkeywords%3Dlarge%20language%20model%26origin%3DSWITCH_SEARCH_VERTICAL%26sid%3DV2J
2025-05-15 17:07:35,517 - linkedin_collector - WARNING - No response from Lix for keyword large language model
2025-05-15 17:07:36,520 - linkedin_collector - INFO - Collecting LinkedIn posts for keyword: gpt
2025-05-15 17:07:36,719 - linkedin_collector - ERROR - Error making request to lix: 402 Client Error: Payment Required for url: https://api.lix-it.com/v1/li/linkedin/search/posts?url=https%3A%2F%2Fwww.linkedin.com%2Fsearch%2Fresults%2Fcontent%2F%3Fkeywords%3Dgpt%26origin%3DSWITCH_SEARCH_VERTICAL%26sid%3DV2J
2025-05-15 17:07:36,720 - linkedin_collector - WARNING - No response from Lix for keyword gpt
2025-05-15 17:07:37,725 - linkedin_collector - INFO - Collecting LinkedIn posts for keyword: generative ai
2025-05-15 17:07:37,969 - linkedin_collector - ERROR - Error making request to lix: 402 Client Error: Payment Required for url: https://api.lix-it.com/v1/li/linkedin/search/posts?url=https%3A%2F%2Fwww.linkedin.com%2Fsearch%2Fresults%2Fcontent%2F%3Fkeywords%3Dgenerative%20ai%26origin%3DSWITCH_SEARCH_VERTICAL%26sid%3DV2J
2025-05-15 17:07:37,970 - linkedin_collector - WARNING - No response from Lix for keyword generative ai
2025-05-15 17:07:38,974 - linkedin_collector - INFO - Collected 0 unique LinkedIn posts
2025-05-15 17:08:32,318 - linkedin_collector - INFO - LinkedIn collector initialized with lix service
