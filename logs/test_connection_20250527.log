2025-05-27 17:02:05,802 - test_connection - INFO - Testing connection to SQL Server at 23.29.129.76
2025-05-27 17:02:05,802 - test_connection - INFO - Database: aicontent, User: dhairya_mac
2025-05-27 17:02:06,181 - test_connection - ERROR - Connection failed: dlopen(/Users/<USER>/Documents/AI Dashboard/venv/lib/python3.9/site-packages/pyodbc.cpython-39-darwin.so, 0x0002): symbol not found in flat namespace '_SQLAllocHandle'
2025-05-27 17:03:06,640 - test_connection - INFO - Testing connection to SQL Server at 23.29.129.76
2025-05-27 17:03:06,640 - test_connection - INFO - Database: aicontent, User: dhairya_mac
2025-05-27 17:03:08,297 - test_connection - ERROR - Connection failed: (pyodbc.OperationalError) ('08001', '[08001] [FreeTDS][SQL Server]Unable to connect to data source (0) (SQLDriverConnect)')
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-27 17:04:57,591 - test_connection - INFO - Testing connection to SQL Server at 23.29.129.76
2025-05-27 17:04:57,591 - test_connection - INFO - Database: aicontent, User: dhairya_mac
2025-05-27 17:04:57,603 - test_connection - ERROR - Connection failed: (sqlite3.OperationalError) unrecognized token: "@"
[SQL: SELECT @@VERSION as version]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-27 17:08:46,168 - test_connection - INFO - Testing connection to database at 23.29.129.76
2025-05-27 17:08:46,168 - test_connection - INFO - Database: aicontent, User: dhairya_mac
2025-05-27 17:08:46,168 - test_connection - INFO - Connection URL: mssql+pyodbc://dhairya_mac:q1w2e3r4t5%21@***
2025-05-27 17:08:48,053 - test_connection - INFO - Successfully connected to SQL Server!
2025-05-27 17:08:48,054 - test_connection - INFO - Server version: Microsoft SQL Server 2022 (RTM) - 16.0.1000.6 (X64) 
	Oct  8 2022 05:58:25 
	Copyright (C) 2022 Microsoft Corporation
	Express Edition (64-bit) on Windows Server 2022 Datacenter 10.0 <X64> (Build 20348: ) (Hypervisor)

2025-05-27 17:08:48,077 - test_connection - ERROR - Connection failed: (pyodbc.ProgrammingError) ('24000', '[24000] [FreeTDS][SQL Server]Invalid cursor state (0) (SQLExecDirectW)')
[SQL: SELECT DB_NAME() as current_db]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-05-27 17:09:08,108 - test_connection - INFO - Testing connection to database at 23.29.129.76
2025-05-27 17:09:08,108 - test_connection - INFO - Database: aicontent, User: dhairya_mac
2025-05-27 17:09:08,108 - test_connection - INFO - Connection URL: mssql+pyodbc://dhairya_mac:q1w2e3r4t5%21@***
2025-05-27 17:09:09,606 - test_connection - INFO - Successfully connected to SQL Server!
2025-05-27 17:09:09,607 - test_connection - INFO - Server version: Microsoft SQL Server 2022 (RTM) - 16.0.1000.6 (X64) 
	Oct  8 2022 05:58:25 
	Copyright (C) 2022 Microsoft Corporation
	Express Edition (64-bit) on Windows Server 2022 Datacenter 10.0 <X64> (Build 20348: ) (Hypervisor)

2025-05-27 17:09:09,706 - test_connection - INFO - Current database: aicontent
2025-05-27 17:28:28,744 - test_connection - INFO - Testing connection to database at 23.29.129.76
2025-05-27 17:28:28,744 - test_connection - INFO - Database: aicontent, User: dhairya_mac
2025-05-27 17:28:28,744 - test_connection - INFO - Connection URL: mssql+pyodbc://dhairya_mac:q1w2e3r4t5%21@***
2025-05-27 17:28:30,499 - test_connection - INFO - Successfully connected to SQL Server!
2025-05-27 17:28:30,499 - test_connection - INFO - Server version: Microsoft SQL Server 2022 (RTM) - 16.0.1000.6 (X64) 
	Oct  8 2022 05:58:25 
	Copyright (C) 2022 Microsoft Corporation
	Express Edition (64-bit) on Windows Server 2022 Datacenter 10.0 <X64> (Build 20348: ) (Hypervisor)

2025-05-27 17:28:30,606 - test_connection - INFO - Current database: aicontent
2025-05-27 17:32:48,825 - test_connection - INFO - Testing connection to SQL Server at 23.29.129.76
2025-05-27 17:32:48,825 - test_connection - INFO - Database: aicontent, User: dhairya_mac
2025-05-27 17:32:50,832 - test_connection - INFO - Successfully connected to SQL Server!
2025-05-27 17:32:50,832 - test_connection - INFO - Server version: Microsoft SQL Server 2022 (RTM) - 16.0.1000.6 (X64) 
	Oct  8 2022 05:58:25 
	Copyright (C) 2022 Microsoft Corporation
	Express Edition (64-bit) on Windows Server 2022 Datacenter 10.0 <X64> (Build 20348: ) (Hypervisor)

2025-05-27 17:32:50,861 - test_connection - ERROR - Connection failed: (pyodbc.ProgrammingError) ('24000', '[24000] [FreeTDS][SQL Server]Invalid cursor state (0) (SQLExecDirectW)')
[SQL: SELECT DB_NAME() as current_db]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-05-27 17:32:59,677 - test_connection - INFO - Testing connection to database at 23.29.129.76
2025-05-27 17:32:59,678 - test_connection - INFO - Database: aicontent, User: dhairya_mac
2025-05-27 17:32:59,678 - test_connection - INFO - Connection URL: mssql+pyodbc://dhairya_mac:q1w2e3r4t5%21@***
2025-05-27 17:33:01,550 - test_connection - INFO - Successfully connected to SQL Server!
2025-05-27 17:33:01,551 - test_connection - INFO - Server version: Microsoft SQL Server 2022 (RTM) - 16.0.1000.6 (X64) 
	Oct  8 2022 05:58:25 
	Copyright (C) 2022 Microsoft Corporation
	Express Edition (64-bit) on Windows Server 2022 Datacenter 10.0 <X64> (Build 20348: ) (Hypervisor)

2025-05-27 17:33:01,724 - test_connection - INFO - Current database: aicontent
2025-05-27 19:29:35,712 - test_connection - INFO - Testing connection to database at 23.29.129.76
2025-05-27 19:29:35,712 - test_connection - INFO - Database: aicontent, User: dhairya_mac
2025-05-27 19:29:35,712 - test_connection - INFO - Connection URL: mssql+pyodbc://dhairya_mac:q1w2e3r4t5%21@***
2025-05-27 19:29:36,146 - test_connection - ERROR - Connection failed: dlopen(/Users/<USER>/Documents/AI Dashboard/venv/lib/python3.9/site-packages/pyodbc.cpython-39-darwin.so, 0x0002): symbol not found in flat namespace '_SQLAllocHandle'
2025-05-27 19:29:45,708 - test_connection - INFO - Testing connection to database at 23.29.129.76
2025-05-27 19:29:45,708 - test_connection - INFO - Database: aicontent, User: dhairya_mac
2025-05-27 19:29:45,708 - test_connection - INFO - Connection URL: mssql+pyodbc://dhairya_mac:q1w2e3r4t5%21@***
2025-05-27 19:29:48,287 - test_connection - INFO - Successfully connected to SQL Server!
2025-05-27 19:29:48,287 - test_connection - INFO - Server version: Microsoft SQL Server 2022 (RTM) - 16.0.1000.6 (X64) 
	Oct  8 2022 05:58:25 
	Copyright (C) 2022 Microsoft Corporation
	Express Edition (64-bit) on Windows Server 2022 Datacenter 10.0 <X64> (Build 20348: ) (Hypervisor)

2025-05-27 19:29:48,519 - test_connection - INFO - Current database: aicontent
